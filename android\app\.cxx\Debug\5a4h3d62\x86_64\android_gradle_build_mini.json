{"buildFiles": ["C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\android\\app\\.cxx\\Debug\\5a4h3d62\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\android\\app\\.cxx\\Debug\\5a4h3d62\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "x86_64", "output": "C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\5a4h3d62\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a89f88737dcaf880ea21b9fdcb02480\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "x86_64", "output": "C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\5a4h3d62\\obj\\x86_64\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a89f88737dcaf880ea21b9fdcb02480\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "x86_64", "output": "C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\5a4h3d62\\obj\\x86_64\\libappmodules.so", "runtimeFiles": ["C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\5a4h3d62\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "C:\\MyDrive\\Projects\\Zingtill\\mobile\\Zingtill-Mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\5a4h3d62\\obj\\x86_64\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a89f88737dcaf880ea21b9fdcb02480\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f5f3eb598334922b3ec7ac664aab84b4\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e": {"artifactName": "react_codegen_RNEdgeToEdge", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "x86_64", "runtimeFiles": []}}}