import React from 'react';
import { Pressable, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useViewMode } from '../context/ViewModeContext';
import { useTheme } from '../context/ThemeContext';

export default function ViewModeToggle() {
  const { viewMode, toggleViewMode } = useViewMode();
  const { theme } = useTheme();
  
  return (
    <Pressable 
      style={styles.container} 
      onPress={toggleViewMode}
      hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
    >
      <Ionicons 
        name={viewMode === 'list' ? 'grid-outline' : 'list-outline'} 
        size={24} 
        color={theme.primary} 
      />
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 4,
  },
});
