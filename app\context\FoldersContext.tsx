import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Alert } from "react-native";

interface Folder {
  id: string;
  name: string;
  parentId: string | null;
  createdAt: Date;
  updatedAt: Date;
  isBookmarked?: boolean;
}

interface FoldersContextType {
  folders: Folder[];
  createFolder: (name: string, parentId: string | null) => Promise<void>;
  updateFolder: (id: string, name: string) => Promise<void>;
  deleteFolder: (id: string) => Promise<void>;
  getFolderPath: (id: string) => { path: string[]; pathNames: string[] };
  toggleBookmark: (id: string) => Promise<void>;
}

const STORAGE_KEY = "@folders";

const FoldersContext = createContext<FoldersContextType | undefined>(undefined);

export function FoldersProvider({ children }: { children: React.ReactNode }) {
  const [folders, setFolders] = useState<Folder[]>([]);

  // Load folders from AsyncStorage on mount
  useEffect(() => {
    loadFolders();
  }, []);

  const loadFolders = async () => {
    try {
      const storedFolders = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedFolders) {
        const parsedFolders = JSON.parse(storedFolders);
        // Convert string dates back to Date objects
        const foldersWithDates = parsedFolders.map((folder: any) => ({
          ...folder,
          createdAt: new Date(folder.createdAt),
          updatedAt: new Date(folder.updatedAt),
        }));
        setFolders(foldersWithDates);
      }
    } catch (error) {
      console.error("Error loading folders:", error);
      Alert.alert("Error", "Failed to load folders");
    }
  };

  const saveFolders = async (updatedFolders: Folder[]) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedFolders));
    } catch (error) {
      console.error("Error saving folders:", error);
      Alert.alert("Error", "Failed to save folders");
    }
  };

  const createFolder = useCallback(
    async (name: string, parentId: string | null) => {
      const newFolder: Folder = {
        id: Date.now().toString(),
        name,
        parentId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      const updatedFolders = [...folders, newFolder];
      setFolders(updatedFolders);
      await saveFolders(updatedFolders);
    },
    [folders]
  );

  const updateFolder = useCallback(
    async (id: string, name: string) => {
      const updatedFolders = folders.map((folder) =>
        folder.id === id ? { ...folder, name, updatedAt: new Date() } : folder
      );
      setFolders(updatedFolders);
      await saveFolders(updatedFolders);
    },
    [folders]
  );

  const deleteFolder = useCallback(
    async (id: string) => {
      // Get all descendant folders (subfolders)
      const getDescendantIds = (folderId: string): string[] => {
        const descendants: string[] = [];
        folders.forEach((folder) => {
          if (folder.parentId === folderId) {
            descendants.push(folder.id);
            descendants.push(...getDescendantIds(folder.id));
          }
        });
        return descendants;
      };

      const descendantIds = getDescendantIds(id);
      const idsToRemove = [id, ...descendantIds];

      const updatedFolders = folders.filter(
        (folder) => !idsToRemove.includes(folder.id)
      );
      setFolders(updatedFolders);
      await saveFolders(updatedFolders);
    },
    [folders]
  );

  const getFolderPath = useCallback(
    (id: string) => {
      const path: string[] = [];
      const pathNames: string[] = [];
      let currentFolder = folders.find((f) => f.id === id);

      while (currentFolder) {
        path.unshift(currentFolder.id);
        pathNames.unshift(currentFolder.name);
        currentFolder = folders.find((f) => f.id === currentFolder?.parentId);
      }

      return { path, pathNames };
    },
    [folders]
  );

  const toggleBookmark = useCallback(
    async (id: string) => {
      const updatedFolders = folders.map((folder) =>
        folder.id === id
          ? { ...folder, isBookmarked: !folder.isBookmarked }
          : folder
      );
      setFolders(updatedFolders);
      await saveFolders(updatedFolders);
    },
    [folders]
  );

  return (
    <FoldersContext.Provider
      value={{
        folders,
        createFolder,
        updateFolder,
        deleteFolder,
        getFolderPath,
        toggleBookmark,
      }}
    >
      {children}
    </FoldersContext.Provider>
  );
}

export const useFolders = () => {
  const context = useContext(FoldersContext);
  if (!context) {
    throw new Error("useFolders must be used within a FoldersProvider");
  }
  return context;
};
