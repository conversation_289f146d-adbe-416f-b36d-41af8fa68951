import React, { createContext, useContext, useState, useEffect } from "react";
import { useColorScheme } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Define theme colors
export const lightTheme = {
  background: "#f2f2f7",
  card: "#FFFFFF",
  text: "#000000",
  secondaryText: "#8E8E93",
  border: "#E5E5EA",
  primary: "#007AFF",
  accent: "#FF9500",
  danger: "#FF3B30",
  tabBar: "#FFFFFF",
  statusBar: "light",
  navbarBackground: "#FFFFFF",
};

export const darkTheme = {
  background: "#121212",
  card: "#1E1E1E",
  text: "#FFFFFF",
  secondaryText: "#A9A9A9",
  border: "#2C2C2C",
  primary: "#0A84FF",
  accent: "#FF9F0A",
  danger: "#FF453A",
  tabBar: "#1E1E1E",
  statusBar: "dark",
  navbarBackground: "#1E1E1E",
};

type Theme = typeof lightTheme;

interface ThemeContextType {
  theme: Theme;
  isDarkMode: boolean;
  toggleTheme: () => void;
  setDarkMode: (value: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType>({
  theme: lightTheme,
  isDarkMode: false,
  toggleTheme: () => {},
  setDarkMode: () => {},
});

export const useTheme = () => useContext(ThemeContext);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const systemColorScheme = useColorScheme();
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Load theme preference from storage on mount
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const storedTheme = await AsyncStorage.getItem("theme_preference");
        if (storedTheme !== null) {
          setIsDarkMode(storedTheme === "dark");
        } else {
          // If no stored preference, use system preference
          setIsDarkMode(systemColorScheme === "dark");
        }
      } catch (error) {
        console.error("Failed to load theme preference:", error);
      }
    };

    loadThemePreference();
  }, [systemColorScheme]);

  // Save theme preference when it changes
  useEffect(() => {
    const saveThemePreference = async () => {
      try {
        await AsyncStorage.setItem(
          "theme_preference",
          isDarkMode ? "dark" : "light"
        );
      } catch (error) {
        console.error("Failed to save theme preference:", error);
      }
    };

    saveThemePreference();
  }, [isDarkMode]);

  const toggleTheme = () => {
    setIsDarkMode((prev) => !prev);
  };

  const theme = isDarkMode ? darkTheme : lightTheme;

  return (
    <ThemeContext.Provider
      value={{ theme, isDarkMode, toggleTheme, setDarkMode: setIsDarkMode }}
    >
      {children}
    </ThemeContext.Provider>
  );
}
