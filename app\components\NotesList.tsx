import {
  View,
  Text,
  FlatList,
  Pressable,
  StyleSheet,
  Dimensions,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { Ionicons } from "@expo/vector-icons";
import { useNotes } from "../context/NotesContext";
import { useTheme } from "../context/ThemeContext";
import { useViewMode } from "../context/ViewModeContext";
import type { Note } from "../types/globalTypes";

const SCREEN_WIDTH = Dimensions.get("window").width;
const PADDING = 15;
const GAP = 18;
const ITEM_WIDTH = (SCREEN_WIDTH - PADDING * 2 - GAP) / 2;

interface NotesListProps {
  notes: Note[];
  onScroll?: (event: any) => void;
  scrollEventThrottle?: number;
}

export default function NotesList({
  notes,
  onScroll,
  scrollEventThrottle,
}: NotesListProps) {
  const { toggleBookmark } = useNotes();
  const navigation = useNavigation<StackNavigationProp<any>>();
  const { theme } = useTheme();
  const { viewMode } = useViewMode();
  const isGridView = viewMode === "grid";

  const formatDate = (date: Date) => {
    const now = new Date();
    const noteDate = new Date(date);

    if (noteDate.toDateString() === now.toDateString()) {
      return new Intl.DateTimeFormat("en-US", {
        hour: "numeric",
        minute: "numeric",
      }).format(noteDate);
    }

    if (noteDate.getFullYear() === now.getFullYear()) {
      return new Intl.DateTimeFormat("en-US", {
        weekday: "long",
      }).format(noteDate);
    }

    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    }).format(noteDate);
  };

  const renderItem = ({ item }: { item: Note }) => {
    // Create dynamic styles based on the current theme
    const dynamicStyles = {
      noteItem: {
        backgroundColor: theme.card,
        borderColor: theme.border,
      },
      noteTitle: {
        color: theme.text,
      },
      noteDate: {
        color: theme.secondaryText,
      },
      noteText: {
        color: theme.secondaryText,
      },
    };

    if (isGridView) {
      // Grid view item
      return (
        <Pressable
          style={[styles.noteItem, dynamicStyles.noteItem]}
          onPress={() => navigation.navigate("Note", { id: item.id })}
        >
          <View style={styles.noteContent}>
            <View style={styles.noteHeader}>
              <Text
                style={[styles.noteTitle, dynamicStyles.noteTitle]}
                numberOfLines={1}
              >
                {item.title}
              </Text>
              <Pressable
                style={styles.bookmarkButton}
                onPress={(e) => {
                  e.stopPropagation();
                  toggleBookmark(item.id);
                }}
              >
                <Ionicons
                  name={item.isBookmarked ? "bookmark" : "bookmark-outline"}
                  size={20}
                  color={item.isBookmarked ? theme.accent : theme.secondaryText}
                />
              </Pressable>
            </View>
            <Text style={[styles.noteDate, dynamicStyles.noteDate]}>
              {formatDate(item.createdAt)}
            </Text>
            <Text
              style={[styles.noteText, dynamicStyles.noteText]}
              numberOfLines={4}
            >
              {item.content}
            </Text>
          </View>
        </Pressable>
      );
    } else {
      // List view item
      return (
        <Pressable
          style={[styles.listItem, { backgroundColor: theme.card }]}
          onPress={() => navigation.navigate("Note", { id: item.id })}
        >
          <View
            style={[
              styles.listItemContent,
              { borderBottomColor: theme.border },
            ]}
          >
            <Ionicons name="document-text" size={24} color={theme.primary} />
            <View style={styles.listItemDetails}>
              <Text
                style={[styles.listItemTitle, { color: theme.text }]}
                numberOfLines={1}
              >
                {item.title}
              </Text>
              <Text
                style={[styles.listItemDate, { color: theme.secondaryText }]}
              >
                {formatDate(item.createdAt)}
              </Text>
            </View>
            <View style={styles.listItemActions}>
              <Pressable
                style={styles.actionButton}
                onPress={(e) => {
                  e.stopPropagation();
                  toggleBookmark(item.id);
                }}
              >
                <Ionicons
                  name={item.isBookmarked ? "bookmark" : "bookmark-outline"}
                  size={20}
                  color={item.isBookmarked ? theme.accent : theme.secondaryText}
                />
              </Pressable>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={theme.secondaryText}
              />
            </View>
          </View>
        </Pressable>
      );
    }
  };

  return (
    <FlatList
      data={notes}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      numColumns={isGridView ? 2 : 1}
      columnWrapperStyle={isGridView ? styles.row : undefined}
      contentContainerStyle={styles.list}
      showsVerticalScrollIndicator={false}
      onScroll={onScroll}
      scrollEventThrottle={scrollEventThrottle}
      key={viewMode} // Force re-render when view mode changes
    />
  );
}

const styles = StyleSheet.create({
  list: {
    padding: PADDING,
    paddingBottom: 80, // Add extra padding at the bottom for the tab bar
  },
  row: {
    justifyContent: "space-between",
    marginBottom: GAP,
  },
  // Grid view styles
  noteItem: {
    width: ITEM_WIDTH,
    height: ITEM_WIDTH * 1.1,
    backgroundColor: "#ffffff",
    borderRadius: 10,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  noteContent: {
    flex: 1,
    padding: 12,
  },
  noteHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  noteTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#000",
    flex: 1,
  },
  bookmarkButton: {
    padding: 4,
  },
  noteDate: {
    fontSize: 13,
    color: "#666",
    marginBottom: 8,
  },
  noteText: {
    fontSize: 15,
    color: "#666",
    lineHeight: 20,
  },
  // List view styles
  listItem: {
    backgroundColor: "#FFFFFF",
    marginBottom: 8,
    borderRadius: 10,
    overflow: "hidden",
  },
  listItemContent: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: "#E5E5EA",
  },
  listItemDetails: {
    flex: 1,
    marginLeft: 12,
  },
  listItemTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#000000",
    marginBottom: 4,
  },
  listItemDate: {
    fontSize: 12,
    color: "#8E8E93",
  },
  listItemActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButton: {
    padding: 8,
    marginRight: 4,
  },
});
