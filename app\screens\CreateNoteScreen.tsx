import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Pressable,
  Alert,
} from "react-native";
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { Ionicons } from "@expo/vector-icons";
import { useNotes } from "../context/NotesContext";
import { useTheme } from "../context/ThemeContext";
import useOrientation from "../hooks/useOrientation";
import { RootStackParamList } from "../navigation";

type CreateNoteScreenRouteProp = RouteProp<RootStackParamList, "CreateNote">;
type CreateNoteScreenNavigationProp = StackNavigationProp<RootStackParamList>;

export default function CreateNoteScreen() {
  const navigation = useNavigation<CreateNoteScreenNavigationProp>();
  const route = useRoute<CreateNoteScreenRouteProp>();
  // Ensure we properly extract the parentId from route params
  const { parentId } = route.params || {};
  // Convert empty string to null
  const folderId = parentId && parentId !== "" ? parentId : null;

  const { addNote } = useNotes();
  const { theme } = useTheme();
  const orientation = useOrientation();

  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");

  const handleSave = () => {
    if (title.trim() === "") {
      Alert.alert("Error", "Title cannot be empty");
      return;
    }

    try {
      addNote(title.trim(), content.trim(), folderId);
      navigation.goBack();
    } catch (error) {
      Alert.alert("Error", "Failed to create note");
    }
  };

  const handleCancel = () => {
    if (title.trim() !== "" || content.trim() !== "") {
      Alert.alert(
        "Discard Changes",
        "Are you sure you want to discard your changes?",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Discard", onPress: () => navigation.goBack() },
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  // Create dynamic styles based on the current theme and orientation
  const { isLandscape } = orientation;
  const dynamicStyles = {
    container: {
      backgroundColor: theme.background,
    },
    header: {
      backgroundColor: theme.background,
      borderBottomColor: theme.border,
      paddingTop: isLandscape ? 20 : 60, // Less top padding in landscape
    },
    title: {
      color: theme.text,
    },
    cancelText: {
      color: theme.danger,
    },
    saveText: {
      color: theme.primary,
    },
    titleInput: {
      color: theme.text,
      fontSize: isLandscape ? 28 : 24, // Larger title in landscape
    },
    contentInput: {
      color: theme.text,
      fontSize: isLandscape ? 18 : 17, // Slightly larger text in landscape
      lineHeight: isLandscape ? 28 : 24,
    },
    editContainer: {
      padding: isLandscape ? 24 : 16,
    },
  };

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      <View style={[styles.header, dynamicStyles.header]}>
        <Pressable style={styles.cancelButton} onPress={handleCancel}>
          <Text style={[styles.cancelText, dynamicStyles.cancelText]}>
            Cancel
          </Text>
        </Pressable>

        <Text style={[styles.title, dynamicStyles.title]}>New Note</Text>

        <Pressable style={styles.saveButton} onPress={handleSave}>
          <Text style={[styles.saveText, dynamicStyles.saveText]}>Save</Text>
        </Pressable>
      </View>

      <View style={[styles.editContainer, dynamicStyles.editContainer]}>
        <TextInput
          style={[styles.titleInput, dynamicStyles.titleInput]}
          value={title}
          onChangeText={setTitle}
          placeholder="Title"
          placeholderTextColor={theme.secondaryText}
          autoCapitalize="sentences"
          autoFocus
        />
        <TextInput
          style={[styles.contentInput, dynamicStyles.contentInput]}
          value={content}
          onChangeText={setContent}
          placeholder="Start writing..."
          placeholderTextColor={theme.secondaryText}
          multiline
          autoCapitalize="sentences"
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingTop: 60,
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5EA",
  },
  cancelButton: {
    padding: 8,
  },
  cancelText: {
    fontSize: 17,
    color: "#FF3B30",
  },
  title: {
    fontSize: 17,
    fontWeight: "600",
    color: "#000000",
  },
  saveButton: {
    padding: 8,
  },
  saveText: {
    fontSize: 17,
    fontWeight: "600",
    color: "#007AFF",
  },
  editContainer: {
    flex: 1,
    padding: 16,
  },
  titleInput: {
    fontSize: 24,
    fontWeight: "600",
    color: "#000000",
    marginBottom: 16,
    padding: 0,
  },
  contentInput: {
    flex: 1,
    fontSize: 17,
    color: "#000000",
    lineHeight: 24,
    textAlignVertical: "top",
    padding: 0,
  },
});
