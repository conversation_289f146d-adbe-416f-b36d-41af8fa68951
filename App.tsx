import "react-native-gesture-handler";
import React, { useEffect } from "react";
import { View, Platform, StatusBar as RNStatusBar } from "react-native";
import Constants from "expo-constants";
import { StatusBar } from "expo-status-bar";
import * as SystemUI from "expo-system-ui";
import changeNavigationBarColor from 'react-native-navigation-bar-color';

import { NotesProvider } from "./app/context/NotesContext";
import { FoldersProvider } from "./app/context/FoldersContext";
import { CurrentFolderProvider } from "./app/context/CurrentFolderContext";
import { ThemeProvider, useTheme } from "./app/context/ThemeContext";
import { ViewModeProvider } from "./app/context/ViewModeContext";
import { TabBarVisibilityProvider } from "./app/context/TabBarVisibilityContext";
import Navigation from "./app/navigation";

const ThemedStatusBar = () => {
  const { isDarkMode } = useTheme();

  useEffect(() => {
    if (Platform.OS === "android") {
      // Set navigation bar color (bottom)
      // SystemUI.setBackgroundColorAsync(isDarkMode ? "black" : "white");
      changeNavigationBarColor(isDarkMode ? '#000000' : '#ffffff', false);

      // Set status bar background color
      RNStatusBar.setBackgroundColor(isDarkMode ? "black" : "white", true);
    }
  }, [isDarkMode]);

  return (
    <>
      {/* Render a View under the status bar to apply background color */}
      {/* {Platform.OS === "android" && (
        <View
          style={{
            height: Constants.statusBarHeight,
            backgroundColor: isDarkMode ? "black" : "white",
          }}
        />
      )} */}
      <StatusBar style={isDarkMode ? "light" : "dark"} />
    </>
  );
};

export default function App() {
  return (
    <NotesProvider>
      <FoldersProvider>
        <CurrentFolderProvider>
          <ThemeProvider>
            <ViewModeProvider>
              <TabBarVisibilityProvider>
                <ThemedStatusBar />
                <Navigation />
              </TabBarVisibilityProvider>
            </ViewModeProvider>
          </ThemeProvider>
        </CurrentFolderProvider>
      </FoldersProvider>
    </NotesProvider>
  );
}
