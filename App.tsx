import "react-native-gesture-handler";
import React, { useEffect } from "react";
import { StatusBar } from "expo-status-bar";
import * as SystemUI from "expo-system-ui";
import { NotesProvider } from "./app/context/NotesContext";
import { FoldersProvider } from "./app/context/FoldersContext";
import { CurrentFolderProvider } from "./app/context/CurrentFolderContext";
import { ThemeProvider, useTheme } from "./app/context/ThemeContext";
import { ViewModeProvider } from "./app/context/ViewModeContext";
import { TabBarVisibilityProvider } from "./app/context/TabBarVisibilityContext";
import Navigation from "./app/navigation";
import { Platform } from "react-native";

const ThemedStatusBar = () => {
  const { isDarkMode } = useTheme();
  
  // Set both status bar and navigation bar colors
  // useEffect(() => {
  //   if (Platform.OS === 'android') {
  //     SystemUI.setBackgroundColorAsync(isDarkMode ? "black" : "white");
  //   }
  // }, [isDarkMode]);
  
  return <StatusBar style={isDarkMode ? "light" : "dark"} />;
};

export default function App() {
  return (
    <NotesProvider>
      <FoldersProvider>
        <CurrentFolderProvider>
          <ThemeProvider>
            <ViewModeProvider>
              <TabBarVisibilityProvider>
                <ThemedStatusBar />
                <Navigation />
              </TabBarVisibilityProvider>
            </ViewModeProvider>
          </ThemeProvider>
        </CurrentFolderProvider>
      </FoldersProvider>
    </NotesProvider>
  );
}
