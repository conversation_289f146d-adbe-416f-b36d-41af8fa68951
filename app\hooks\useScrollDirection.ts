import { useState, useRef } from 'react';
import { NativeSyntheticEvent, NativeScrollEvent } from 'react-native';
import { useTabBarVisibility } from '../context/TabBarVisibilityContext';

/**
 * A hook that detects scroll direction and controls tab bar visibility
 * @returns An object with onScroll function to be used with ScrollView or FlatList
 */
export default function useScrollDirection() {
  // Get the setIsVisible function from the TabBarVisibilityContext
  const { setIsVisible } = useTabBarVisibility();
  
  // Store the last scroll position
  const lastScrollY = useRef(0);
  
  // Store the current scroll direction (up or down)
  const [isScrollingDown, setIsScrollingDown] = useState(false);
  
  // Threshold for how much scroll is needed before changing direction
  const THRESHOLD = 10;
  
  // Handle scroll events
  const onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentScrollY = event.nativeEvent.contentOffset.y;
    
    // Determine if scrolling up or down by comparing with last position
    if (currentScrollY <= 0) {
      // At the top of the scroll view, always show the tab bar
      setIsVisible(true);
    } else if (Math.abs(currentScrollY - lastScrollY.current) > THRESHOLD) {
      // Only update direction if we've scrolled more than the threshold
      const isDown = currentScrollY > lastScrollY.current;
      
      if (isDown !== isScrollingDown) {
        setIsScrollingDown(isDown);
        setIsVisible(!isDown); // Hide tab bar when scrolling down, show when scrolling up
      }
      
      // Update the last scroll position
      lastScrollY.current = currentScrollY;
    }
  };
  
  return {
    onScroll,
    isScrollingDown,
  };
}
