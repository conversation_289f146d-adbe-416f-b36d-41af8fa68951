import React, { createContext, useContext, useState, useEffect } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Dimensions, Platform } from "react-native";

export type ViewMode = "list" | "grid";

interface ViewModeContextType {
  viewMode: ViewMode;
  toggleViewMode: () => void;
  setViewMode: (mode: ViewMode) => void;
  getGridColumnCount: () => number;
  isTablet: boolean;
}

const ViewModeContext = createContext<ViewModeContextType>({
  viewMode: "list",
  toggleViewMode: () => {},
  setViewMode: () => {},
  getGridColumnCount: () => 2,
  isTablet: false,
});

export const useViewMode = () => useContext(ViewModeContext);

export function ViewModeProvider({ children }: { children: React.ReactNode }) {
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [dimensions, setDimensions] = useState(Dimensions.get("window"));

  // Detect if device is a tablet based on screen size and pixel density
  const detectTablet = () => {
    const { width, height } = dimensions;
    const screenSize = Math.sqrt(width * width + height * height) / 160;
    return screenSize >= 7; // Consider devices with screen size >= 7 inches as tablets
  };

  const isTablet = detectTablet();

  // Update dimensions when orientation changes
  useEffect(() => {
    const subscription = Dimensions.addEventListener("change", ({ window }) => {
      setDimensions(window);
    });

    return () => subscription.remove();
  }, []);

  // Get the number of columns based on device type and orientation
  const getGridColumnCount = () => {
    if (!isTablet) {
      return 2; // Default 2 columns for phones
    }

    const { width, height } = dimensions;
    const isLandscape = width > height;

    return isLandscape ? 4 : 3; // 3 columns in portrait, 4 in landscape for tablets
  };

  // Load saved view mode preference when component mounts
  useEffect(() => {
    const loadViewModePreference = async () => {
      try {
        const savedViewMode = await AsyncStorage.getItem(
          "view_mode_preference"
        );
        if (savedViewMode === "grid" || savedViewMode === "list") {
          setViewMode(savedViewMode);
        }
      } catch (error) {
        console.error("Failed to load view mode preference:", error);
      }
    };

    loadViewModePreference();
  }, []);

  // Save view mode preference when it changes
  useEffect(() => {
    const saveViewModePreference = async () => {
      try {
        await AsyncStorage.setItem("view_mode_preference", viewMode);
      } catch (error) {
        console.error("Failed to save view mode preference:", error);
      }
    };

    saveViewModePreference();
  }, [viewMode]);

  const toggleViewMode = () => {
    setViewMode(viewMode === "list" ? "grid" : "list");
  };

  return (
    <ViewModeContext.Provider
      value={{
        viewMode,
        toggleViewMode,
        setViewMode,
        getGridColumnCount,
        isTablet,
      }}
    >
      {children}
    </ViewModeContext.Provider>
  );
}
