import { View, Text, Pressable, StyleSheet } from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

export default function CreateScreen() {
  const { parentId } = useLocalSearchParams<{ parentId: string }>();

  const handleCreateNote = () => {
    router.push({
      pathname: "/note/new",
      params: { parentId: parentId || "" }
    });
  };

  const handleCreateFolder = () => {
    router.push({
      pathname: "/folder/new",
      params: { parentId: parentId || "" }
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Create New</Text>
      
      <View style={styles.optionsContainer}>
        <Pressable 
          style={styles.option} 
          onPress={handleCreateNote}
        >
          <View style={[styles.iconContainer, { backgroundColor: '#007AFF' }]}>
            <Ionicons name="document-text" size={24} color="#fff" />
          </View>
          <View style={styles.optionTextContainer}>
            <Text style={styles.optionTitle}>New Note</Text>
            <Text style={styles.optionDescription}>Create a new note</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
        </Pressable>

        <Pressable 
          style={styles.option}
          onPress={handleCreateFolder}
        >
          <View style={[styles.iconContainer, { backgroundColor: '#FF9500' }]}>
            <Ionicons name="folder" size={24} color="#fff" />
          </View>
          <View style={styles.optionTextContainer}>
            <Text style={styles.optionTitle}>New Folder</Text>
            <Text style={styles.optionDescription}>Create a new folder to organize notes</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#C7C7CC" />
        </Pressable>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f2f2f7',
    padding: 16,
    paddingTop: 60,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 24,
  },
  optionsContainer: {
    gap: 12,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 10,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 17,
    fontWeight: '600',
  },
  optionDescription: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 2,
  },
});

