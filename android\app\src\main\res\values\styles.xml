<resources xmlns:tools="http://schemas.android.com/tools">
  <style name="AppTheme" parent="Theme.EdgeToEdge">
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#ffffff</item>
    <item name="android:windowLightNavigationBar">true</item>
    <item name="android:navigationBarColor">@color/navigationBarColor</item>
  </style>
  <style name="Theme.App.SplashScreen" parent="AppTheme">
    <item name="android:windowBackground">@drawable/ic_launcher_background</item>
  </style>
</resources>