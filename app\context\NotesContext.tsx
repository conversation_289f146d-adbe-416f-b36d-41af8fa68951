import React, { createContext, useContext, useState, useEffect } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Alert } from "react-native";

interface Note {
  id: string;
  title: string;
  content: string;
  folderId: string | null; // Add folderId to track parent folder
  createdAt: Date;
  updatedAt: Date;
  category?: string;
  tags: string[];
  isBookmarked?: boolean;
}

interface NotesContextType {
  notes: Note[];
  addNote: (
    title: string,
    content: string,
    folderId: string | null,
    category?: string,
    tags?: string[]
  ) => void;
  updateNote: (id: string, updates: Partial<Note>) => void;
  deleteNote: (id: string) => void;
  searchNotes: (query: string, folderId?: string | null) => Note[];
  getFolderNotes: (folderId: string | null) => Note[];
  toggleBookmark: (id: string) => void;
}

const NOTES_STORAGE_KEY = "@notes";

const NotesContext = createContext<NotesContextType | undefined>(undefined);

export function NotesProvider({ children }: { children: React.ReactNode }) {
  const [notes, setNotes] = useState<Note[]>([]);

  useEffect(() => {
    loadNotes();
  }, []);

  const loadNotes = async () => {
    try {
      const storedNotes = await AsyncStorage.getItem(NOTES_STORAGE_KEY);
      if (storedNotes) {
        const parsedNotes = JSON.parse(storedNotes);
        const notesWithDates = parsedNotes.map((note: any) => ({
          ...note,
          createdAt: new Date(note.createdAt),
          updatedAt: new Date(note.updatedAt),
        }));
        setNotes(notesWithDates);
      }
    } catch (error) {
      console.error("Error loading notes:", error);
      Alert.alert("Error", "Failed to load notes");
    }
  };

  const saveNotes = async (updatedNotes: Note[]) => {
    try {
      await AsyncStorage.setItem(
        NOTES_STORAGE_KEY,
        JSON.stringify(updatedNotes)
      );
    } catch (error) {
      console.error("Error saving notes:", error);
      Alert.alert("Error", "Failed to save notes");
    }
  };

  const addNote = (
    title: string,
    content: string,
    folderId: string | null,
    category?: string,
    tags: string[] = []
  ) => {
    const newNote: Note = {
      id: Date.now().toString(),
      title,
      content,
      folderId, // This will now correctly store the folder ID
      createdAt: new Date(),
      updatedAt: new Date(),
      category,
      tags,
      isBookmarked: false,
    };
    const updatedNotes = [newNote, ...notes];
    setNotes(updatedNotes);
    saveNotes(updatedNotes);
  };

  const getFolderNotes = (folderId: string | null) => {
    return notes.filter((note) => note.folderId === folderId);
  };

  const updateNote = (id: string, updates: Partial<Note>) => {
    const updatedNotes = notes.map((note) =>
      note.id === id ? { ...note, ...updates } : note
    );
    setNotes(updatedNotes);
    saveNotes(updatedNotes);
  };

  const deleteNote = (id: string) => {
    const updatedNotes = notes.filter((note) => note.id !== id);
    setNotes(updatedNotes);
    saveNotes(updatedNotes);
  };

  const searchNotes = (query: string) => {
    const searchTerm = query.toLowerCase();
    return notes.filter(
      (note) =>
        note.title.toLowerCase().includes(searchTerm) ||
        note.content.toLowerCase().includes(searchTerm) ||
        note.tags.some((tag) => tag.toLowerCase().includes(searchTerm)) ||
        note.category?.toLowerCase().includes(searchTerm)
    );
  };

  const toggleBookmark = (id: string) => {
    const updatedNotes = notes.map((note) =>
      note.id === id ? { ...note, isBookmarked: !note.isBookmarked } : note
    );
    setNotes(updatedNotes);
    saveNotes(updatedNotes);
  };

  return (
    <NotesContext.Provider
      value={{
        notes,
        addNote,
        updateNote,
        deleteNote,
        searchNotes,
        getFolderNotes,
        toggleBookmark,
      }}
    >
      {children}
    </NotesContext.Provider>
  );
}

export function useNotes() {
  const context = useContext(NotesContext);
  if (context === undefined) {
    throw new Error("useNotes must be used within a NotesProvider");
  }
  return context;
}
