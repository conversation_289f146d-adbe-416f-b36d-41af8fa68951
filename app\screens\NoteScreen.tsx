import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  Pressable,
  Alert,
} from "react-native";
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { Ionicons } from "@expo/vector-icons";
import { useNotes } from "../context/NotesContext";
import { useTheme } from "../context/ThemeContext";
import useOrientation from "../hooks/useOrientation";
import { HomeStackParamList } from "../navigation/HomeStackParamList";
import { SearchStackParamList } from "../navigation/SearchStackParamList";
import { BookmarksStackParamList } from "../navigation/BookmarksStackParamList";
import { ParamListBase } from "@react-navigation/native";

// Use a union type to handle all possible stack param lists
type NoteParamList =
  | HomeStackParamList
  | SearchStackParamList
  | BookmarksStackParamList;

type NoteScreenRouteProp = RouteProp<ParamListBase, "Note">;
type NoteScreenNavigationProp = StackNavigationProp<ParamListBase>;

export default function NoteScreen() {
  const navigation = useNavigation<NoteScreenNavigationProp>();
  const route = useRoute<NoteScreenRouteProp>();
  const { id } = route.params as { id: string };

  const { notes, updateNote, deleteNote, toggleBookmark } = useNotes();
  const { theme } = useTheme();
  const orientation = useOrientation();

  const [note, setNote] = useState<any>(null);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    const foundNote = notes.find((note) => note.id === id);
    if (foundNote) {
      setNote(foundNote);
      setTitle(foundNote.title);
      setContent(foundNote.content);
    }
  }, [id, notes]);

  const handleSave = async () => {
    if (title.trim() === "") {
      Alert.alert("Error", "Title cannot be empty");
      return;
    }

    try {
      await updateNote(id, {
        title: title.trim(),
        content: content.trim(),
      });
      setIsEditing(false);

      // Refresh note data
      const updatedNote = notes.find((note) => note.id === id);
      if (updatedNote) {
        setNote(updatedNote);
      }
    } catch (error) {
      Alert.alert("Error", "Failed to save note");
    }
  };

  const handleDelete = () => {
    Alert.alert(
      "Delete Note",
      `Are you sure you want to delete "${note?.title}"?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              await deleteNote(id);
              navigation.goBack();
            } catch (error) {
              Alert.alert("Error", "Failed to delete note");
            }
          },
        },
      ]
    );
  };

  const handleToggleBookmark = async () => {
    try {
      await toggleBookmark(id);

      // Refresh note data
      const updatedNote = notes.find((note) => note.id === id);
      if (updatedNote) {
        setNote(updatedNote);
      }
    } catch (error) {
      Alert.alert("Error", "Failed to toggle bookmark");
    }
  };

  // Create dynamic styles based on the current theme and orientation
  const { isLandscape } = orientation;
  const dynamicStyles = {
    container: {
      backgroundColor: theme.background,
    },
    header: {
      backgroundColor: theme.background,
      borderBottomColor: theme.border,
      paddingTop: isLandscape ? 20 : 60, // Less top padding in landscape
    },
    backButtonText: {
      color: theme.primary,
    },
    title: {
      color: theme.text,
    },
    titleText: {
      color: theme.text,
      fontSize: isLandscape ? 28 : 24, // Larger title in landscape
    },
    dateText: {
      color: theme.secondaryText,
    },
    contentText: {
      color: theme.text,
      fontSize: isLandscape ? 18 : 17, // Slightly larger text in landscape
      lineHeight: isLandscape ? 28 : 24,
    },
    emptyText: {
      color: theme.secondaryText,
    },
    titleInput: {
      color: theme.text,
      fontSize: isLandscape ? 28 : 24,
    },
    contentInput: {
      color: theme.text,
      fontSize: isLandscape ? 18 : 17,
      lineHeight: isLandscape ? 28 : 24,
    },
    saveText: {
      color: theme.primary,
    },
    viewContainer: {
      padding: isLandscape ? 24 : 16,
    },
    editContainer: {
      padding: isLandscape ? 24 : 16,
    },
  };

  if (!note) {
    return (
      <View style={[styles.container, dynamicStyles.container]}>
        <View style={[styles.header, dynamicStyles.header]}>
          <Pressable
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="chevron-back" size={24} color={theme.primary} />
            <Text style={[styles.backButtonText, dynamicStyles.backButtonText]}>
              Back
            </Text>
          </Pressable>
          <Text style={[styles.title, dynamicStyles.title]}>Note</Text>
        </View>
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, dynamicStyles.emptyText]}>
            Note not found
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      <View style={[styles.header, dynamicStyles.header]}>
        <Pressable
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color={theme.primary} />
          <Text style={[styles.backButtonText, dynamicStyles.backButtonText]}>
            Back
          </Text>
        </Pressable>

        <View style={styles.headerActions}>
          <Pressable style={styles.headerButton} onPress={handleToggleBookmark}>
            <Ionicons
              name={note.isBookmarked ? "bookmark" : "bookmark-outline"}
              size={24}
              color={theme.primary}
            />
          </Pressable>
          <Pressable style={styles.headerButton} onPress={handleDelete}>
            <Ionicons name="trash-outline" size={24} color={theme.danger} />
          </Pressable>
          {isEditing ? (
            <Pressable style={styles.headerButton} onPress={handleSave}>
              <Text style={[styles.saveText, dynamicStyles.saveText]}>
                Save
              </Text>
            </Pressable>
          ) : (
            <Pressable
              style={styles.headerButton}
              onPress={() => setIsEditing(true)}
            >
              <Ionicons name="create-outline" size={24} color={theme.primary} />
            </Pressable>
          )}
        </View>
      </View>

      {isEditing ? (
        <View style={[styles.editContainer, dynamicStyles.editContainer]}>
          <TextInput
            style={[styles.titleInput, dynamicStyles.titleInput]}
            value={title}
            onChangeText={setTitle}
            placeholder="Title"
            placeholderTextColor={theme.secondaryText}
            autoCapitalize="sentences"
          />
          <TextInput
            style={[styles.contentInput, dynamicStyles.contentInput]}
            value={content}
            onChangeText={setContent}
            placeholder="Start writing..."
            placeholderTextColor={theme.secondaryText}
            multiline
            autoCapitalize="sentences"
          />
        </View>
      ) : (
        <ScrollView style={[styles.viewContainer, dynamicStyles.viewContainer]}>
          <Text style={[styles.titleText, dynamicStyles.titleText]}>
            {note.title}
          </Text>
          <Text style={[styles.dateText, dynamicStyles.dateText]}>
            {new Date(note.updatedAt || note.createdAt).toLocaleString()}
          </Text>
          <Text style={[styles.contentText, dynamicStyles.contentText]}>
            {note.content}
          </Text>

          {/* Special layout for landscape mode with long content */}
          {isLandscape && note.content.length > 500 && (
            <View style={{ height: 100 }} /> // Extra space at bottom for scrolling in landscape
          )}
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingTop: 60,
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5EA",
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
  },
  backButtonText: {
    fontSize: 17,
    color: "#007AFF",
  },
  title: {
    fontSize: 17,
    fontWeight: "600",
    color: "#000000",
    flex: 1,
    textAlign: "center",
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerButton: {
    padding: 8,
    marginLeft: 4,
  },
  saveText: {
    fontSize: 17,
    fontWeight: "600",
    color: "#007AFF",
  },
  editContainer: {
    flex: 1,
    padding: 16,
  },
  titleInput: {
    fontSize: 24,
    fontWeight: "600",
    color: "#000000",
    marginBottom: 8,
    padding: 0,
  },
  contentInput: {
    flex: 1,
    fontSize: 17,
    color: "#000000",
    lineHeight: 24,
    textAlignVertical: "top",
    padding: 0,
  },
  viewContainer: {
    flex: 1,
    padding: 16,
  },
  titleText: {
    fontSize: 24,
    fontWeight: "600",
    color: "#000000",
    marginBottom: 8,
  },
  dateText: {
    fontSize: 14,
    color: "#8E8E93",
    marginBottom: 16,
  },
  contentText: {
    fontSize: 17,
    color: "#000000",
    lineHeight: 24,
  },
  emptyContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyText: {
    fontSize: 16,
    color: "#8E8E93",
  },
});
