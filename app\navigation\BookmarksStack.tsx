import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { View } from "react-native";
import { useTheme } from "../context/ThemeContext";
import BookmarksScreen from "../screens/BookmarksScreen";
import FolderScreen from "../screens/FolderScreen";
import NoteScreen from "../screens/NoteScreen";

export type BookmarksStackParamList = {
  BookmarksMain: undefined;
  Folder: { id: string };
  Note: { id: string };
};

const Stack = createStackNavigator<BookmarksStackParamList>();

export default function BookmarksStack() {
  const { theme } = useTheme();

  return (
    <View style={{ flex: 1, backgroundColor: theme.background }}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          // Disable animations for immediate transitions
          animation: "none",
          // Set background color to match theme
          cardStyle: { backgroundColor: theme.background },
          // In case animations can't be fully disabled
          cardStyleInterpolator: ({ current }) => ({
            cardStyle: {
              opacity: current.progress,
              backgroundColor: theme.background,
            },
          }),
          // Prevent white flash during transitions
          detachPreviousScreen: false,
        }}
      >
        <Stack.Screen name="BookmarksMain" component={BookmarksScreen} />
        <Stack.Screen name="Folder" component={FolderScreen} />
        <Stack.Screen name="Note" component={NoteScreen} />
      </Stack.Navigator>
    </View>
  );
}
