import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Pressable,
  Alert,
} from "react-native";
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { Ionicons } from "@expo/vector-icons";
import { useFolders } from "../context/FoldersContext";
import { useTheme } from "../context/ThemeContext";
import { RootStackParamList } from "../navigation";

type CreateFolderScreenRouteProp = RouteProp<
  RootStackParamList,
  "CreateFolder"
>;
type CreateFolderScreenNavigationProp = StackNavigationProp<RootStackParamList>;

export default function CreateFolderScreen() {
  const navigation = useNavigation<CreateFolderScreenNavigationProp>();
  const route = useRoute<CreateFolderScreenRouteProp>();
  // Ensure we properly extract the parentId from route params
  const { parentId } = route.params || {};
  // Convert empty string to null
  const folderId = parentId && parentId !== "" ? parentId : null;

  const { createFolder } = useFolders();
  const { theme } = useTheme();
  const [folderName, setFolderName] = useState("Untitled");

  const handleSubmit = async () => {
    if (folderName.trim() === "") {
      Alert.alert("Error", "Folder name cannot be empty");
      return;
    }

    try {
      await createFolder(folderName.trim(), folderId);
      navigation.goBack();
    } catch (error) {
      Alert.alert("Error", "Failed to create folder");
    }
  };

  const handleCancel = () => {
    if (folderName.trim() !== "Untitled") {
      Alert.alert(
        "Discard Changes",
        "Are you sure you want to discard your changes?",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Discard", onPress: () => navigation.goBack() },
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  // Create dynamic styles based on the current theme
  const dynamicStyles = {
    container: {
      backgroundColor: theme.background,
    },
    header: {
      backgroundColor: theme.background,
      borderBottomColor: theme.border,
    },
    title: {
      color: theme.text,
    },
    cancelText: {
      color: theme.danger,
    },
    createText: {
      color: theme.primary,
    },
    folderName: {
      color: theme.text,
    },
    input: {
      backgroundColor: theme.card,
      borderColor: theme.border,
      color: theme.text,
    },
  };

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      <View style={[styles.header, dynamicStyles.header]}>
        <Pressable style={styles.cancelButton} onPress={handleCancel}>
          <Text style={[styles.cancelText, dynamicStyles.cancelText]}>
            Cancel
          </Text>
        </Pressable>

        <Text style={[styles.title, dynamicStyles.title]}>New Folder</Text>

        <Pressable style={styles.createButton} onPress={handleSubmit}>
          <Text style={[styles.createText, dynamicStyles.createText]}>
            Create
          </Text>
        </Pressable>
      </View>

      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Ionicons name="folder" size={64} color={theme.accent} />
        </View>
        <Text style={[styles.folderName, dynamicStyles.folderName]}>
          {folderName}
        </Text>
        <TextInput
          style={[styles.input, dynamicStyles.input]}
          value={folderName}
          onChangeText={setFolderName}
          placeholder="Untitled"
          placeholderTextColor={theme.secondaryText}
          autoCapitalize="sentences"
          autoFocus
          selectTextOnFocus
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f2f2f7",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingTop: 60,
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5EA",
  },
  cancelButton: {
    padding: 8,
  },
  cancelText: {
    fontSize: 17,
    color: "#FF3B30",
  },
  title: {
    fontSize: 17,
    fontWeight: "600",
    color: "#000000",
  },
  createButton: {
    padding: 8,
  },
  createText: {
    fontSize: 17,
    fontWeight: "600",
    color: "#007AFF",
  },
  content: {
    alignItems: "center",
    padding: 24,
  },
  iconContainer: {
    marginBottom: 16,
  },
  folderName: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 16,
  },
  input: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    padding: 12,
    fontSize: 17,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: "#c6c6c8",
  },
});
