import React, { createContext, useContext, useState } from 'react';

type CurrentFolderContextType = {
  currentFolderId: string | null;
  setCurrentFolderId: (id: string | null) => void;
};

const CurrentFolderContext = createContext<CurrentFolderContextType>({
  currentFolderId: null,
  setCurrentFolderId: () => {},
});

export const useCurrentFolder = () => useContext(CurrentFolderContext);

export function CurrentFolderProvider({ children }: { children: React.ReactNode }) {
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);

  return (
    <CurrentFolderContext.Provider value={{ currentFolderId, setCurrentFolderId }}>
      {children}
    </CurrentFolderContext.Provider>
  );
}
