# Android build
android/app/build/
android/build/
android/.gradle/

# iOS build
ios/build/
ios/Pods/
ios/DerivedData/

# Node modules and other existing entries
node_modules/
.expo/
dist/
npm-debug.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
web-build/

# macOS
.DS_Store


# Node.js
node_modules/

# Expo
.expo/
.expo-shared/

# TypeScript
dist/
*.tsbuildinfo

# Metro bundler
*.bundle
*.map
*.pack
*.tmp

# macOS & system
.DS_Store
Thumbs.db

# Android
android/.gradle/
android/app/build/
android/build/
android/local.properties
android/.idea/

# iOS
ios/build/
ios/Pods/
ios/Podfile.lock
ios/DerivedData/

# IDEs / Editors
.vscode/
.idea/

# Logs & lockfiles
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# OS-generated
ehthumbs.db
Icon?
📦 Optional: GitHub Actions / CI folders
gitignore
Copy
Edit
# GitHub actions
.github/workflows/
