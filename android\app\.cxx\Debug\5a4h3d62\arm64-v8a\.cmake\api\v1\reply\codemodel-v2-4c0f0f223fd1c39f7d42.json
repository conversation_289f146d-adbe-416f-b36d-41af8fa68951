{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-6aaa15e48f49c5d6ac7f.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-0ec962ca0bd4dcfe6e53.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-1a77f978d61e4753b4ce.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [5]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-a1ea9e1db6d790e4d8f8.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [4]}, {"build": "RNEdgeToEdge_autolinked_build", "jsonFile": "directory-RNEdgeToEdge_autolinked_build-Debug-2ae43d1c9edbcdc49080.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni", "targetIndexes": [1]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-68caa001f3e97214125f.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e", "jsonFile": "target-react_codegen_RNEdgeToEdge-Debug-9c782154e6040c4d1f8a.json", "name": "react_codegen_RNEdgeToEdge", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-6fff92f872924c8dd471.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-a295aac62adcaf3167fc.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-9bf910295ab11ed6c9b1.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-7c762d68a75d7060d11c.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/.cxx/Debug/5a4h3d62/arm64-v8a", "source": "C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}