import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { useTheme } from "../context/ThemeContext";

import TabNavigator from "./TabNavigator";
import CreateNoteScreen from "../screens/CreateNoteScreen";
import CreateFolderScreen from "../screens/CreateFolderScreen";

// Define the root stack parameter list
export type RootStackParamList = {
  Main: undefined;
  CreateNote: { parentId?: string | null };
  CreateFolder: { parentId?: string | null };
};

// Create the stack navigator
const Stack = createStackNavigator<RootStackParamList>();

export default function Navigation() {
  const { theme } = useTheme();

  return (
    <SafeAreaProvider style={{ backgroundColor: theme.background }}>
      <NavigationContainer>
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            // Default to no animation for faster transitions
            animation: "none",
            // Set background color to match theme
            cardStyle: { backgroundColor: theme.background },
            // Prevent white flash during transitions
            detachPreviousScreen: false,
          }}
        >
          <Stack.Screen name="Main" component={TabNavigator} />
          <Stack.Screen
            name="CreateNote"
            component={CreateNoteScreen}
            options={{
              // Keep modal animation for create screens
              animation: "slide_from_bottom",
              presentation: "modal",
            }}
          />
          <Stack.Screen
            name="CreateFolder"
            component={CreateFolderScreen}
            options={{
              // Keep modal animation for create screens
              animation: "slide_from_bottom",
              presentation: "modal",
            }}
          />
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}
