{"expo": {"name": "zing<PERSON>l", "slug": "zing<PERSON>l", "version": "1.0.0", "scheme": "com.zingtill.app", "orientation": "default", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "androidNavigationBar": {"barStyle": "dark-content", "backgroundColor": "#121212"}, "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "newArchEnabled": true, "bundleIdentifier": "com.zingtill.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.zingtill.app", "newArchEnabled": true, "edgeToEdgeEnabled": true}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router"]}}