import React from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Alert,
  Dimensions,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { useFolders } from "../context/FoldersContext";
import { useNotes } from "../context/NotesContext";
import { useTheme } from "../context/ThemeContext";
import { useViewMode } from "../context/ViewModeContext";
// import { RootStackParamList } from "../navigation"; // Not needed with the any type
import Navbar from "../components/Navbar";
import ViewModeToggle from "../components/ViewModeToggle";
import useOrientation from "../hooks/useOrientation";
import GridViewItem from "../components/GridViewItem";
import ListViewItem from "../components/ListViewItem";

// Define a more permissive navigation type that allows navigating to any screen
type HomeScreenNavigationProp = StackNavigationProp<any>;

export default function HomeScreen() {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const {
    folders,
    toggleBookmark: toggleFolderBookmark,
    deleteFolder,
  } = useFolders();
  const { notes, toggleBookmark: toggleNoteBookmark, deleteNote } = useNotes();
  const { theme } = useTheme();
  const { viewMode, getGridColumnCount } = useViewMode();
  const orientation = useOrientation();

  // Get root folders and notes
  const rootFolders = folders.filter((folder) => folder.parentId === null);
  const rootNotes = notes.filter((note) => note.folderId === null);

  // Combine folders and notes into a single array
  const items = [
    ...rootFolders.map((folder) => ({ ...folder, type: "folder" })),
    ...rootNotes.map((note) => ({ ...note, type: "note" })),
  ].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  const handleBookmark = (item: any) => {
    if (item.type === "folder") {
      toggleFolderBookmark(item.id);
    } else {
      toggleNoteBookmark(item.id);
    }
  };

  const handleDelete = (item: any) => {
    const isFolder = item.type === "folder";
    const itemName = isFolder ? item.name : item.title;

    Alert.alert(
      `Delete ${isFolder ? "Folder" : "Note"}`,
      `Are you sure you want to delete "${itemName}"?${
        isFolder ? " This will also delete all contents inside." : ""
      }`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => {
            if (isFolder) {
              deleteFolder(item.id);
            } else {
              deleteNote(item.id);
            }
          },
        },
      ]
    );
  };

  const renderItem = ({ item }: { item: any }) => {
    const isFolder = item.type === "folder";
    const isGridView = viewMode === "grid";
    const columnCount = isGridView ? getGridColumnCount() : 1;

    // Get screen width to calculate item width
    const screenWidth = Dimensions.get("window").width;
    // Calculate padding and gaps
    const horizontalPadding = orientation.isLandscape ? 24 : 16;
    const totalPadding = horizontalPadding * 2; // Left and right padding
    const gapBetweenItems = 16;
    const totalGapWidth = gapBetweenItems * (columnCount - 1);

    // Calculate item width based on screen size, padding, and gap
    const itemWidth = isGridView
      ? (screenWidth - totalPadding - totalGapWidth) / columnCount
      : screenWidth - totalPadding;

    // Handle navigation
    const handlePress = () => {
      if (isFolder) {
        navigation.navigate("Folder", { id: item.id });
      } else {
        navigation.navigate("Note", { id: item.id });
      }
    };

    if (isGridView) {
      // Use the GridViewItem component for grid view
      return (
        <GridViewItem
          item={item}
          theme={theme}
          onPress={handlePress}
          onDelete={() => handleDelete(item)}
          onBookmark={() => handleBookmark(item)}
          width={itemWidth}
        />
      );
    } else {
      // Use the ListViewItem component for list view
      return (
        <ListViewItem
          item={item}
          theme={theme}
          onPress={handlePress}
          onDelete={() => handleDelete(item)}
          onBookmark={() => handleBookmark(item)}
        />
      );
    }
  };

  // Create dynamic styles for the container and empty state
  const dynamicStyles = {
    container: {
      backgroundColor: theme.background,
    },
    emptyText: {
      color: theme.secondaryText,
    },
    listContent: {
      // Adjust padding based on orientation
      padding: orientation.isLandscape ? 24 : 16,
    },
  };

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      <Navbar rightComponent={<ViewModeToggle />} />
      <FlatList
        data={items}
        renderItem={renderItem}
        keyExtractor={(item) => `${item.type}-${item.id}`}
        contentContainerStyle={[
          styles.listContent,
          dynamicStyles.listContent,
          viewMode === "grid" && styles.gridContainer,
        ]}
        numColumns={viewMode === "grid" ? getGridColumnCount() : 1}
        key={`${viewMode}-${
          orientation.isLandscape ? "landscape" : "portrait"
        }-${getGridColumnCount()}`}
        columnWrapperStyle={viewMode === "grid" ? styles.gridRow : undefined}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, dynamicStyles.emptyText]}>
              No folders or notes yet
            </Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "rgb(245, 245, 245)",
  },
  header: {
    paddingTop: 16,
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: "#f2f2f7",
  },
  title: {
    fontSize: 34,
    fontWeight: "bold",
    color: "#000000",
  },
  listContent: {
    padding: 16,
    paddingBottom: 80, // Add extra padding at the bottom for the tab bar
  },
  item: {
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    marginBottom: 16,
    overflow: "hidden",
    // Apple Notes style with subtle border
    borderWidth: 0.5,
    borderColor: "#E5E5EA",
  },
  itemContent: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    minHeight: 80, // Ensure minimum height for all items
  },
  itemDetails: {
    flex: 1,
    marginLeft: 12,
  },
  itemTitle: {
    fontSize: 17,
    fontWeight: "600" as const,
    color: "#000000",
    marginBottom: 4,
  },
  itemDate: {
    fontSize: 12,
    color: "#8E8E93",
    marginTop: 2,
  },
  itemActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: "#8E8E93",
  },
  // Style for the footer in grid view items
  gridItemFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    paddingTop: 8,
    paddingHorizontal: 8,
    marginTop: 8,
    borderTopWidth: 1,
  },
  gridContainer: {
    paddingHorizontal: 8,
  },
  gridRow: {
    justifyContent: "space-between",
    marginBottom: 0, // Remove default margin as it's handled in GridViewItem
  },
});
