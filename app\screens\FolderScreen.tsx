import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Pressable,
  Alert,
  Modal,
  Dimensions,
} from "react-native";
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { Ionicons } from "@expo/vector-icons";
import { useFolders } from "../context/FoldersContext";
import { useNotes } from "../context/NotesContext";
import { useCurrentFolder } from "../context/CurrentFolderContext";
import { useTheme } from "../context/ThemeContext";
import { useViewMode } from "../context/ViewModeContext";
import { ParamListBase } from "@react-navigation/native";
import Navbar from "../components/Navbar";
import ViewModeToggle from "../components/ViewModeToggle";
import useScrollDirection from "../hooks/useScrollDirection";
import useOrientation from "../hooks/useOrientation";
import GridViewItem from "../components/GridViewItem";
import ListViewItem from "../components/ListViewItem";

type FolderScreenRouteProp = RouteProp<ParamListBase, "Folder">;
type FolderScreenNavigationProp = StackNavigationProp<ParamListBase>;

export default function FolderScreen() {
  const navigation = useNavigation<FolderScreenNavigationProp>();
  const route = useRoute<FolderScreenRouteProp>();
  const { id } = route.params as { id: string };
  const { setCurrentFolderId } = useCurrentFolder();

  const {
    folders,
    toggleBookmark: toggleFolderBookmark,
    deleteFolder,
  } = useFolders();
  const { notes, toggleBookmark: toggleNoteBookmark, deleteNote } = useNotes();
  const { theme } = useTheme();
  const { viewMode, getGridColumnCount } = useViewMode();
  const orientation = useOrientation();

  // Use the scroll direction hook to control tab bar visibility
  const { onScroll } = useScrollDirection();

  // State for the action menu
  const [isActionMenuVisible, setIsActionMenuVisible] = useState(false);

  // Set current folder ID when component mounts
  useEffect(() => {
    setCurrentFolderId(id);

    // Clear current folder ID when component unmounts
    return () => {
      setCurrentFolderId(null);
    };
  }, [id, setCurrentFolderId]);

  const folder = folders.find((f) => f.id === id);
  const subFolders = folders.filter((f) => f.parentId === id);
  const folderNotes = notes.filter((note) => note.folderId === id);

  // Combine folders and notes into a single array
  const items = [
    ...subFolders.map((folder) => ({ ...folder, type: "folder" })),
    ...folderNotes.map((note) => ({ ...note, type: "note" })),
  ].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  const handleBookmark = (item: any) => {
    if (item.type === "folder") {
      toggleFolderBookmark(item.id);
    } else {
      toggleNoteBookmark(item.id);
    }
  };

  const handleDelete = (item: any) => {
    const isFolder = item.type === "folder";
    const itemName = isFolder ? item.name : item.title;

    Alert.alert(
      `Delete ${isFolder ? "Folder" : "Note"}`,
      `Are you sure you want to delete "${itemName}"?${
        isFolder ? " This will also delete all contents inside." : ""
      }`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => {
            if (isFolder) {
              deleteFolder(item.id);
            } else {
              deleteNote(item.id);
            }
          },
        },
      ]
    );
  };

  const renderItem = ({ item }: { item: any }) => {
    const isFolder = item.type === "folder";
    const isGridView = viewMode === "grid";
    const columnCount = isGridView ? getGridColumnCount() : 1;

    // Get screen width to calculate item width
    const screenWidth = Dimensions.get("window").width;
    // Calculate padding and gaps
    const horizontalPadding = orientation.isLandscape ? 24 : 16;
    const totalPadding = horizontalPadding * 2; // Left and right padding
    const gapBetweenItems = 16;
    const totalGapWidth = gapBetweenItems * (columnCount - 1);

    // Calculate item width based on screen size, padding, and gap
    const itemWidth = isGridView
      ? (screenWidth - totalPadding - totalGapWidth) / columnCount
      : screenWidth - totalPadding;

    // Handle navigation
    const handlePress = () => {
      if (isFolder) {
        navigation.navigate("Folder", { id: item.id });
      } else {
        navigation.navigate("Note", { id: item.id });
      }
    };

    if (isGridView) {
      // Use the GridViewItem component for grid view
      return (
        <GridViewItem
          item={item}
          theme={theme}
          onPress={handlePress}
          onDelete={() => handleDelete(item)}
          onBookmark={() => handleBookmark(item)}
          width={itemWidth}
        />
      );
    } else {
      // Use the ListViewItem component for list view
      return (
        <ListViewItem
          item={item}
          theme={theme}
          onPress={handlePress}
          onDelete={() => handleDelete(item)}
          onBookmark={() => handleBookmark(item)}
        />
      );
    }
  };

  const renderBreadcrumb = () => {
    const getFolderPath = (folderId: string) => {
      const path = [];
      let currentFolder = folders.find((f) => f.id === folderId);

      while (currentFolder) {
        path.unshift(currentFolder);
        currentFolder = folders.find((f) => f.id === currentFolder?.parentId);
      }

      return path;
    };

    const folderPath = getFolderPath(id);

    return (
      <View style={styles.breadcrumb}>
        <Pressable
          style={styles.breadcrumbItem}
          onPress={() => navigation.navigate("HomeMain")}
        >
          <Ionicons name="home-outline" size={16} color="#007AFF" />
          <Text style={[styles.breadcrumbText, dynamicStyles.breadcrumbText]}>
            Home
          </Text>
        </Pressable>

        {folderPath.map((folder, index) => (
          <React.Fragment key={folder.id}>
            <Text
              style={[
                styles.breadcrumbSeparator,
                dynamicStyles.breadcrumbSeparator,
              ]}
            >
              /
            </Text>
            <Pressable
              style={styles.breadcrumbItem}
              onPress={() => {
                if (index < folderPath.length - 1) {
                  navigation.navigate("Folder", { id: folder.id });
                }
              }}
            >
              <Text
                style={[
                  styles.breadcrumbText,
                  dynamicStyles.breadcrumbText,
                  index === folderPath.length - 1 && styles.currentBreadcrumb,
                  index === folderPath.length - 1 &&
                    dynamicStyles.currentBreadcrumb,
                ]}
              >
                {folder.name}
              </Text>
            </Pressable>
          </React.Fragment>
        ))}
      </View>
    );
  };

  const handleCreateNote = () => {
    // Navigate to CreateNote screen with the current folder ID as parentId
    navigation.navigate("CreateNote", { parentId: id });
  };

  const handleCreateFolder = () => {
    // Navigate to CreateFolder screen with the current folder ID as parentId
    navigation.navigate("CreateFolder", { parentId: id });
  };

  // Create dynamic styles for the container and components
  const dynamicStyles = {
    container: {
      backgroundColor: theme.background,
    },
    header: {
      backgroundColor: theme.background,
    },
    title: {
      color: theme.text,
    },
    breadcrumbText: {
      color: theme.primary,
    },
    currentBreadcrumb: {
      color: theme.text,
    },
    breadcrumbSeparator: {
      color: theme.secondaryText,
    },
    emptyText: {
      color: theme.secondaryText,
    },
    listContent: {
      // Adjust padding based on orientation
      padding: orientation.isLandscape ? 24 : 16,
    },
  };

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      <Navbar
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
        rightComponent={<ViewModeToggle />}
      />
      <View style={[styles.header, dynamicStyles.header]}>
        <Text style={[styles.title, dynamicStyles.title]}>
          {folder?.name || "Folder"}
        </Text>
      </View>

      {renderBreadcrumb()}

      <FlatList
        data={items}
        renderItem={renderItem}
        keyExtractor={(item) => `${item.type}-${item.id}`}
        contentContainerStyle={[
          styles.listContent,
          dynamicStyles.listContent,
          viewMode === "grid" && styles.gridContainer,
        ]}
        // Add scroll handler to control tab bar visibility
        onScroll={onScroll}
        scrollEventThrottle={16} // Update every ~16ms for 60fps
        // Adjust number of columns based on view mode and device type
        numColumns={viewMode === "grid" ? getGridColumnCount() : 1}
        key={`${viewMode}-${
          orientation.isLandscape ? "landscape" : "portrait"
        }-${getGridColumnCount()}`}
        columnWrapperStyle={viewMode === "grid" ? styles.gridRow : undefined}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, dynamicStyles.emptyText]}>
              This folder is empty
            </Text>
          </View>
        }
      />

      {/* Floating Action Button for creating content within the folder */}

      {/* <Pressable
        style={styles.fab}
        onPress={() => setIsActionMenuVisible(true)}
      >
        <Ionicons name="add" size={24} color="#FFFFFF" />
      </Pressable> */}

      {/* Action menu for creating notes or folders */}
      <Modal
        visible={isActionMenuVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setIsActionMenuVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.card }]}>
            <Text style={[styles.modalTitle, { color: theme.text }]}>
              Create in {folder?.name || "Folder"}
            </Text>

            <Pressable
              style={[
                styles.modalOption,
                { backgroundColor: theme.background },
              ]}
              onPress={() => {
                setIsActionMenuVisible(false);
                handleCreateNote();
              }}
            >
              <Ionicons
                name="document-text-outline"
                size={24}
                color={theme.primary}
              />
              <Text style={[styles.modalOptionText, { color: theme.text }]}>
                New Note
              </Text>
            </Pressable>

            <Pressable
              style={[
                styles.modalOption,
                { backgroundColor: theme.background },
              ]}
              onPress={() => {
                setIsActionMenuVisible(false);
                handleCreateFolder();
              }}
            >
              <Ionicons name="folder-outline" size={24} color={theme.accent} />
              <Text style={[styles.modalOptionText, { color: theme.text }]}>
                New Folder
              </Text>
            </Pressable>

            <Pressable
              style={[
                styles.cancelButton,
                { backgroundColor: theme.background },
              ]}
              onPress={() => setIsActionMenuVisible(false)}
            >
              <Text style={[styles.cancelButtonText, { color: theme.danger }]}>
                Cancel
              </Text>
            </Pressable>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f2f2f7",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingTop: 16,
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: "#f2f2f7",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#000000",
    flex: 1,
  },
  breadcrumb: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingBottom: 16,
    flexWrap: "wrap",
  },
  breadcrumbItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  breadcrumbText: {
    fontSize: 14,
    color: "#007AFF",
    marginLeft: 4,
  },
  currentBreadcrumb: {
    fontWeight: "600",
    color: "#000000",
  },
  breadcrumbSeparator: {
    fontSize: 14,
    color: "#8E8E93",
    marginHorizontal: 4,
  },
  listContent: {
    padding: 16,
    paddingBottom: 80, // Add extra padding at the bottom for the tab bar
  },
  emptyContainer: {
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: "#8E8E93",
  },
  // Styles for list and grid view items
  item: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    marginBottom: 16,
    overflow: "hidden",
    // Add shadow for Google Keep-like appearance
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  itemContent: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    minHeight: 80, // Ensure minimum height for all items
  },
  itemDetails: {
    flex: 1,
    marginLeft: 12,
  },
  itemTitle: {
    fontSize: 17,
    fontWeight: "600" as const,
    color: "#000000",
    marginBottom: 4,
  },
  itemDate: {
    fontSize: 12,
    color: "#8E8E93",
    marginTop: 2,
  },
  itemActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  // Style for the footer in grid view items
  gridItemFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    paddingTop: 8,
    paddingHorizontal: 8,
    marginTop: 8,
    borderTopWidth: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 20,
    width: "80%",
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
    textAlign: "center",
  },
  modalOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: "#F2F2F7",
  },
  modalOptionText: {
    fontSize: 16,
    marginLeft: 12,
    color: "#000000",
  },
  cancelButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
    backgroundColor: "#F2F2F7",
    marginTop: 8,
  },
  cancelButtonText: {
    color: "#FF3B30",
    fontSize: 16,
    fontWeight: "600",
  },
  gridContainer: {
    paddingHorizontal: 8,
  },
  gridRow: {
    justifyContent: "space-between",
    marginBottom: 0,
  },
});
