{"name": "zing<PERSON>l", "version": "1.0.0", "private": true, "main": "index.js", "scripts": {"dev": "expo start --web", "android": "expo run:android", "android-cli": "npx react-native run-android", "ios": "expo run:ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.2.10", "expo": "53.0.9", "expo-constants": "~17.1.6", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.23.7", "@expo/webpack-config": "^19.0.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}