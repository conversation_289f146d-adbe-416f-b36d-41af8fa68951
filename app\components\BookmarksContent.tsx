import React from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Alert,
  Dimensions,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { Ionicons } from "@expo/vector-icons";
import { useFolders } from "../context/FoldersContext";
import { useNotes } from "../context/NotesContext";
import { useTheme } from "../context/ThemeContext";
import { useViewMode } from "../context/ViewModeContext";
import { ParamListBase } from "@react-navigation/native";
import useOrientation from "../hooks/useOrientation";
import GridViewItem from "./GridViewItem";
import ListViewItem from "./ListViewItem";

export default function BookmarksContent() {
  const navigation = useNavigation<StackNavigationProp<ParamListBase>>();
  const {
    folders,
    toggleBookmark: toggleFolderBookmark,
    deleteFolder,
  } = useFolders();
  const { notes, toggleBookmark: toggleNoteBookmark, deleteNote } = useNotes();
  const { theme } = useTheme();
  const { viewMode, getGridColumnCount } = useViewMode();
  const orientation = useOrientation();

  // Determine if we should use grid view based on viewMode only
  const isGridView = viewMode === "grid";

  // Get bookmarked folders and notes
  const bookmarkedFolders = folders.filter((folder) => folder.isBookmarked);
  const bookmarkedNotes = notes.filter((note) => note.isBookmarked);

  // Combine bookmarked folders and notes into a single array
  const items = [
    ...bookmarkedFolders.map((folder) => ({ ...folder, type: "folder" })),
    ...bookmarkedNotes.map((note) => ({ ...note, type: "note" })),
  ].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  const renderItem = ({ item }: { item: any }) => {
    const isFolder = item.type === "folder";
    const columnCount = isGridView ? getGridColumnCount() : 1;

    // Get screen width to calculate item width for grid view
    const screenWidth = Dimensions.get("window").width;
    // Calculate padding and gaps
    const horizontalPadding = orientation.isLandscape ? 24 : 16;
    const totalPadding = horizontalPadding * 2; // Left and right padding
    const gapBetweenItems = 16;
    const totalGapWidth = gapBetweenItems * (columnCount - 1);

    // Calculate item width based on screen size, padding, and gap
    const itemWidth = isGridView
      ? (screenWidth - totalPadding - totalGapWidth) / columnCount
      : screenWidth - totalPadding;

    // Handle navigation
    const handlePress = () => {
      if (isFolder) {
        navigation.navigate("Folder", { id: item.id });
      } else {
        navigation.navigate("Note", { id: item.id });
      }
    };

    const handleBookmark = () => {
      if (isFolder) {
        toggleFolderBookmark(item.id);
      } else {
        toggleNoteBookmark(item.id);
      }
    };

    const handleDelete = () => {
      const itemName = isFolder ? item.name : item.title;
      Alert.alert(
        `Delete ${isFolder ? "Folder" : "Note"}`,
        `Are you sure you want to delete "${itemName}"?${
          isFolder ? " This will also delete all contents inside." : ""
        }`,
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Delete",
            style: "destructive",
            onPress: () => {
              if (isFolder) {
                deleteFolder(item.id);
              } else {
                deleteNote(item.id);
              }
            },
          },
        ]
      );
    };

    if (isGridView) {
      // Use the GridViewItem component for grid view
      return (
        <GridViewItem
          item={item}
          theme={theme}
          onPress={handlePress}
          onDelete={handleDelete}
          onBookmark={handleBookmark}
          width={itemWidth}
        />
      );
    } else {
      // Use the ListViewItem component for list view
      return (
        <ListViewItem
          item={item}
          theme={theme}
          onPress={handlePress}
          onDelete={handleDelete}
          onBookmark={handleBookmark}
        />
      );
    }
  };

  // Create dynamic styles for the container and empty state
  const dynamicStyles = {
    container: {
      backgroundColor: theme.background,
    },
    emptyText: {
      color: theme.secondaryText,
    },
  };

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      <FlatList
        data={items}
        renderItem={renderItem}
        keyExtractor={(item) => `${item.type}-${item.id}`}
        numColumns={isGridView ? getGridColumnCount() : 1}
        key={`${viewMode}-${
          orientation.isLandscape ? "landscape" : "portrait"
        }-${getGridColumnCount()}`}
        columnWrapperStyle={isGridView ? styles.gridRow : undefined}
        contentContainerStyle={[
          styles.listContent,
          isGridView && styles.gridContainer,
        ]}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, dynamicStyles.emptyText]}>
              No bookmarks yet
            </Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    padding: 16,
    paddingBottom: 80, // Add extra padding at the bottom for the tab bar
  },
  gridContainer: {
    paddingHorizontal: 8,
  },
  gridRow: {
    justifyContent: "space-between",
    marginBottom: 0,
  },
  item: {
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    marginHorizontal: 16,
    marginVertical: 4,
    overflow: "hidden",
  },
  itemContent: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
  },
  itemDetails: {
    flex: 1,
    marginLeft: 12,
  },
  itemTitle: {
    fontSize: 17,
    color: "#000000",
  },
  itemDate: {
    fontSize: 14,
    color: "#8E8E93",
    marginTop: 2,
  },
  itemActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: "#8E8E93",
  },
});
