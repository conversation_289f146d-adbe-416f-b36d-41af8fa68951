import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { View } from "react-native";
import { useTheme } from "../context/ThemeContext";
import SearchScreen from "../screens/SearchScreen";
import FolderScreen from "../screens/FolderScreen";
import NoteScreen from "../screens/NoteScreen";

export type SearchStackParamList = {
  SearchMain: undefined;
  Folder: { id: string };
  Note: { id: string };
};

const Stack = createStackNavigator<SearchStackParamList>();

export default function SearchStack() {
  const { theme } = useTheme();

  return (
    <View style={{ flex: 1, backgroundColor: theme.background }}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          // Disable animations for immediate transitions
          animation: "none",
          // Set background color to match theme
          cardStyle: { backgroundColor: theme.background },
          // In case animations can't be fully disabled
          cardStyleInterpolator: ({ current }) => ({
            cardStyle: {
              opacity: current.progress,
              backgroundColor: theme.background,
            },
          }),
          // Prevent white flash during transitions
          detachPreviousScreen: false,
        }}
      >
        <Stack.Screen name="SearchMain" component={SearchScreen} />
        <Stack.Screen name="Folder" component={FolderScreen} />
        <Stack.Screen name="Note" component={NoteScreen} />
      </Stack.Navigator>
    </View>
  );
}
