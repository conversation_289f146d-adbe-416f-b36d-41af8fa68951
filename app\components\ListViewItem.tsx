import React from "react";
import { View, Text, Pressable, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface ListViewItemProps {
  item: any;
  theme: any;
  onPress: () => void;
  onDelete?: () => void;
  onBookmark?: () => void;
}

/**
 * A reusable list view item component for displaying folders and notes
 * Used in both HomeScreen and FolderScreen
 */
const ListViewItem: React.FC<ListViewItemProps> = ({
  item,
  theme,
  onPress,
  onDelete,
  onBookmark,
}) => {
  const isFolder = item.type === "folder";

  const handleBookmark = (e: any) => {
    e.stopPropagation();
    if (onBookmark) onBookmark();
  };

  const handleDelete = (e: any) => {
    e.stopPropagation();
    if (onDelete) onDelete();
  };

  return (
    <Pressable
      style={[
        styles.item,
        {
          backgroundColor: theme.background,
        },
      ]}
      onPress={onPress}
    >
      <View
        style={[
          styles.itemContent,
          {
            borderBottomColor: theme.border,
          },
        ]}
      >
        {/* FOLDER ITEM */}
        {isFolder && (
          <>
            {/* Left side - Icon and title */}
            <Ionicons name="folder" size={24} color={theme.accent} />
            <View style={styles.itemDetails}>
              <Text
                style={[styles.title, { color: theme.text }]}
                numberOfLines={1}
              >
                {item.name}
              </Text>
              <Text style={[styles.date, { color: theme.secondaryText }]}>
                {new Date(
                  item.updatedAt || item.createdAt
                ).toLocaleDateString()}
              </Text>
            </View>

            {/* Right side - Actions */}
            <View style={styles.itemActions}>
              <Pressable style={styles.actionButton} onPress={handleBookmark}>
                <Ionicons
                  name={item.isBookmarked ? "bookmark" : "bookmark-outline"}
                  size={20}
                  color={item.isBookmarked ? theme.accent : theme.secondaryText}
                />
              </Pressable>
              <Pressable style={styles.actionButton} onPress={handleDelete}>
                <Ionicons name="trash-outline" size={20} color={theme.danger} />
              </Pressable>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={theme.secondaryText}
              />
            </View>
          </>
        )}

        {/* NOTE ITEM */}
        {!isFolder && (
          <>
            {/* Left side - Icon, title and preview */}
            <Ionicons name="document-text" size={24} color={theme.primary} />
            <View style={styles.itemDetails}>
              <Text
                style={[styles.title, { color: theme.text }]}
                numberOfLines={1}
              >
                {item.title}
              </Text>
              <Text style={[styles.date, { color: theme.secondaryText }]}>
                {new Date(
                  item.updatedAt || item.createdAt
                ).toLocaleDateString()}
              </Text>
            </View>

            {/* Right side - Actions */}
            <View style={styles.itemActions}>
              <Pressable style={styles.actionButton} onPress={handleBookmark}>
                <Ionicons
                  name={item.isBookmarked ? "bookmark" : "bookmark-outline"}
                  size={20}
                  color={item.isBookmarked ? theme.accent : theme.secondaryText}
                />
              </Pressable>
              <Pressable style={styles.actionButton} onPress={handleDelete}>
                <Ionicons name="trash-outline" size={20} color={theme.danger} />
              </Pressable>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={theme.secondaryText}
              />
            </View>
          </>
        )}
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  item: {
    width: "100%",
    marginBottom: 1,
  },
  itemContent: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 0.5,
    minHeight: 80,
  },
  itemDetails: {
    flex: 1,
    marginLeft: 12,
  },
  title: {
    fontSize: 17,
    fontWeight: "600" as const,
    color: "#000000",
    marginBottom: 4,
  },
  date: {
    fontSize: 12,
    color: "#8E8E93",
    marginTop: 2,
  },
  itemActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
});

export default ListViewItem;
