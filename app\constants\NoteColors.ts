// Google Keep-like note colors
export const NOTE_COLORS = [
  '#ffffff', // Default white
  '#f28b82', // Red
  '#fbbc04', // Orange
  '#fff475', // Yellow
  '#ccff90', // Light green
  '#a7ffeb', // Teal
  '#cbf0f8', // Light blue
  '#aecbfa', // Blue
  '#d7aefb', // Purple
  '#fdcfe8', // Pink
  '#e6c9a8', // <PERSON>
  '#e8eaed', // Gray
];

// Function to get a consistent color based on note ID
export const getNoteColor = (id: string): string => {
  // Use the last character of the ID to determine the color
  const lastChar = id.charAt(id.length - 1);
  const colorIndex = parseInt(lastChar, 16) % NOTE_COLORS.length;
  return NOTE_COLORS[colorIndex];
};

// Darker versions of the colors for dark mode
export const DARK_NOTE_COLORS = [
  '#2d2d2d', // Dark gray (instead of white)
  '#5c2b29', // Dark red
  '#614a19', // Dark orange
  '#635d19', // Dark yellow
  '#345920', // Dark green
  '#16504b', // Dark teal
  '#2d555e', // Dark light blue
  '#1e3a5f', // Dark blue
  '#42275e', // Dark purple
  '#5b2245', // Dark pink
  '#442f19', // Dark brown
  '#3c3f43', // Darker gray
];

// Function to get a dark mode color based on note ID
export const getDarkNoteColor = (id: string): string => {
  const lastChar = id.charAt(id.length - 1);
  const colorIndex = parseInt(lastChar, 16) % DARK_NOTE_COLORS.length;
  return DARK_NOTE_COLORS[colorIndex];
};
