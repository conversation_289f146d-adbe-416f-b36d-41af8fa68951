# ninja log v5
33	3876	7704053124068344	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	9615a1f2f5a519a9
31628	38936	7703244281875391	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	f420d1a19309db96
102	11613	7703244008315420	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	4e9ee8518458a338
2	36	0	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/.cxx/Debug/5a4h3d62/x86_64/CMakeFiles/cmake.verify_globs	3dc9bd55e1f5c7ab
43	704	7704152643154371	build.ninja	a0d4852410911ff7
39	9754	7703243989542183	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	e8181560a7d8591b
7401	7719	7704146167966546	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/intermediates/cxx/Debug/5a4h3d62/obj/x86_64/libappmodules.so	46056e04ce6c39b8
23856	40539	7703244297347942	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9cf361c9dcbf08115b4e02e5452f90b9/renderer/components/rnscreens/ComponentDescriptors.cpp.o	8e5f5bfdff12ea17
121	9598	7703243988216133	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	78fda639968e0ccc
131	8779	7703243979807700	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	56251698742bb9e
75	8994	7703243982198669	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	af2220cbfd8d4510
57	11649	7703244008370825	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	6b5bffe72e84b2d5
66	10891	7703244000895246	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	4a72db2acd2c2f25
47	11252	7703244004914706	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	eaa06b55c74977ec
22455	31627	7703244208131545	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/253b30496f2a8e2da1815fbab0122713/react/renderer/components/rnscreens/RNSScreenState.cpp.o	1b406b21af66fe4b
30	12070	7703244013066459	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	a9e53eca3182f05c
84	12301	7703244014649447	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	d3eeaa1e230588f4
21	2864	7704146119555769	CMakeFiles/appmodules.dir/OnLoad.cpp.o	83d9eed2b527409b
112	15018	7703244041888033	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	7f714f123caa228c
93	15703	7703244048646618	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o	408ac2ad5f5aa35f
10893	18697	7703244078574647	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08bf8a5edf40165a2159d743d81643e0/components/safeareacontext/States.cpp.o	b300f688083d2790
11616	20826	7703244100179873	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08bf8a5edf40165a2159d743d81643e0/components/safeareacontext/EventEmitters.cpp.o	6f4591e74de29f51
16	2230	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	90100416ff719c0c
28995	35346	7703244245840406	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	b9065ae255c69e47
12071	27004	7703244162218810	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b2d45095562c584f4d523d595f55b52c/safeareacontext/ComponentDescriptors.cpp.o	35918fb850f5c728
8996	19994	7703244091768170	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	342017f7c37f5ac9
31652	38682	7703244279277687	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9cf361c9dcbf08115b4e02e5452f90b9/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	604b4c212a7f7635
9755	21723	7703244109150609	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1ab3867ab8c19783cafcbaefd2e776c5/safeareacontext/RNCSafeAreaViewState.cpp.o	d0a32df6b0ce9abf
12302	22454	7703244116687827	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0523f5545907f57969e6e42502e3b3ce/safeareacontextJSI-generated.cpp.o	e9ff2f9ae1837b55
11253	23855	7703244130149910	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08bf8a5edf40165a2159d743d81643e0/components/safeareacontext/Props.cpp.o	ba49ff3a54f4f8e8
11650	23918	7703244131408014	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08bf8a5edf40165a2159d743d81643e0/components/safeareacontext/ShadowNodes.cpp.o	60bca745ff75e656
9601	24179	7703244133893139	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1c20be6287af9a6f85cf2b6cefc367bb/RNCSafeAreaViewShadowNode.cpp.o	ca8b0f9559deda02
40	2982	7704053115003916	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	b4365eb0a1b05a77
15019	25661	7703244148771089	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a1ad7a5abd0bec7351bf381686b7cd20/codegen/jni/safeareacontext-generated.cpp.o	76fbb679a1300046
23919	35402	7703244246207328	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	72a5a457e2e436ca
21725	33682	7703244229110542	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4bbd86ab5778fe002729ed3b09dcbe4a/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	e5b3a5579e44b97d
13388	25857	7703244150607841	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/afef62fbe82da558c442c4a47d0ab468/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	47de05c85a55a4b2
33684	39334	7703244285865721	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	edccb4534905d005
27006	27790	7703244169019645	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/intermediates/cxx/Debug/5a4h3d62/obj/x86_64/libreact_codegen_safeareacontext.so	3a76ea17da09233e
15705	27640	7703244168706977	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/afef62fbe82da558c442c4a47d0ab468/components/rnscreens/RNSModalScreenShadowNode.cpp.o	c5b9b684f4fe748e
27	7400	7704146164077439	CMakeFiles/appmodules.dir/C_/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	cd1b74f2baffe12b
19019	28994	7703244182128193	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c4239ea4df0da3e033d10701a99ff33e/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	d75921146c2b09ec
20827	30479	7703244196972829	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/afef62fbe82da558c442c4a47d0ab468/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	5e2352421f72cabb
18699	30759	7703244199664495	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c4239ea4df0da3e033d10701a99ff33e/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	26722f278b0915d8
19995	31650	7703244208714489	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c4239ea4df0da3e033d10701a99ff33e/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	cdab3f777dd48113
27641	34211	7703244234115213	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/10cb91d2992f5c02336040eb029a7fe0/jni/react/renderer/components/rnscreens/States.cpp.o	cf90e7dc2afe20a
27791	35963	7703244251643060	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	a888be7146a1f073
25662	35974	7703244251909736	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/10cb91d2992f5c02336040eb029a7fe0/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	5587ec76d69d1fd
24181	36951	7703244261697695	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/10cb91d2992f5c02336040eb029a7fe0/jni/react/renderer/components/rnscreens/Props.cpp.o	2e34b74399459d1b
30480	37964	7703244272097080	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	d5d1ad23be604b79
30760	38444	7703244276983730	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	f85987d68bf30879
25858	38894	7703244281129276	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/91a4f2b3f2624060758af3aa2879bbf5/react/renderer/components/rnscreens/EventEmitters.cpp.o	c948797e5a0596e
33902	40605	7703244298520984	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	98b7e59e1709951a
40540	40884	7703244301031645	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/intermediates/cxx/Debug/5a4h3d62/obj/x86_64/libreact_codegen_rnscreens.so	94481c9e62293320
26	3979	7704053124989607	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	821c18270a064804
47	3227	7704053117601942	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	cb1bd19659b02a3
70	3758	7704053122774730	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	48a9e752b56aaf23
55	3793	7704053123235173	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	e1d1c288b79f855e
63	3981	7704053124989607	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	47a05f2605e15783
2	47	0	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/.cxx/Debug/5a4h3d62/x86_64/CMakeFiles/cmake.verify_globs	3dc9bd55e1f5c7ab
17	2692	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	90100416ff719c0c
2693	3068	7704152678437941	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/intermediates/cxx/Debug/5a4h3d62/obj/x86_64/libappmodules.so	46056e04ce6c39b8
