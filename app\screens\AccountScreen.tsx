import { View, Text, StyleSheet, Pressable } from "react-native";
import { useNotes } from "../context/NotesContext";

export default function AccountScreen() {
  const { notes } = useNotes();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Account</Text>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{notes.length}</Text>
          <Text style={styles.statLabel}>Total Notes</Text>
        </View>

        <View style={styles.statItem}>
          <Text style={styles.statNumber}>
            {notes.filter((note) => note.isBookmarked).length}
          </Text>
          <Text style={styles.statLabel}>Bookmarked</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    paddingTop: 50,
    paddingHorizontal: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 20,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginTop: 20,
  },
  statItem: {
    alignItems: "center",
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#007AFF",
  },
  statLabel: {
    fontSize: 16,
    color: "#666",
    marginTop: 5,
  },
});
