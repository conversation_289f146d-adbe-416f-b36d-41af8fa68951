import { View, TextInput, StyleSheet } from "react-native";
import { useState } from "react";
import { useNotes } from "../context/NotesContext";
import { useTheme } from "../context/ThemeContext";
import NotesList from "../components/NotesList";
import Navbar from "../components/Navbar";
import ViewModeToggle from "../components/ViewModeToggle";

export default function SearchScreen() {
  const { searchNotes } = useNotes();
  const [searchQuery, setSearchQuery] = useState("");
  const { theme } = useTheme();

  const searchResults = searchQuery ? searchNotes(searchQuery) : [];

  // Create dynamic styles based on the current theme
  const dynamicStyles = {
    container: {
      backgroundColor: theme.background,
    },
    searchInput: {
      backgroundColor: theme.card,
      color: theme.text,
    },
  };

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      <Navbar rightComponent={<ViewModeToggle />} />
      <View style={styles.section}>
        <TextInput
          style={[styles.searchInput, dynamicStyles.searchInput]}
          placeholder="Search notes..."
          placeholderTextColor={theme.secondaryText}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoFocus
        />
        <NotesList notes={searchResults} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  searchInput: {
    backgroundColor: "#f8f8f8",
    padding: 12,
    borderRadius: 8,
    marginBottom: 15,
    fontSize: 16,
  },
  container: {
    flex: 1,
    backgroundColor: "#f2f2f7",
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 12,
    paddingHorizontal: 20,
  },
});
