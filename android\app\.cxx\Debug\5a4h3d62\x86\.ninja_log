# ninja log v5
60	3676	7704053006431861	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	15b7ce7dbe98914e
1	33	0	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/.cxx/Debug/5a4h3d62/x86/CMakeFiles/cmake.verify_globs	247cb93bd461a9e3
48	12469	7703243476520222	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	80208f715c8f42e8
22605	33960	7703243691239165	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c4239ea4df0da3e033d10701a99ff33e/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	3749c3e77d70533f
133	8762	7703243438668776	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	9bc8217c171309d9
67	9391	7703243445277855	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	a2c43ca4b5e51598
29	10428	7703243454960369	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	d476fe2a8fb99ff1
59	695	7704152593340367	build.ninja	5b365a6c8672ce75
106	10253	7703243453619077	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	691f3259f67c61dc
27337	43895	7703243790630458	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9cf361c9dcbf08115b4e02e5452f90b9/renderer/components/rnscreens/ComponentDescriptors.cpp.o	41b9c07e4c25ff71
85	12830	7703243479463740	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	f3fd6b2dab99ba1b
76	12331	7703243474964026	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	c9b65a6c01b526c9
74	3851	7704053008164824	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	13f75be275f01b42
32764	41555	7703243767672014	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	205d8aeff3d59ce9
124	12606	7703243477142607	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	2bc47442f2959558
14971	28309	7703243634745112	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4bbd86ab5778fe002729ed3b09dcbe4a/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	16d50388aee61c3c
38	12922	7703243480942231	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	98eac5bed972718
58	13157	7703243482474671	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	6fa9968d2e0eb93a
16	3020	7704146026718522	CMakeFiles/appmodules.dir/OnLoad.cpp.o	60eded4390f2da65
115	16688	7703243518291407	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	f4674484f1d6a6ef
8224	8512	7704146081379968	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/intermediates/cxx/Debug/5a4h3d62/obj/x86/libappmodules.so	1dd82b3e242abc46
95	17934	7703243529712724	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o	3858f06bb0a44da8
89	3943	7704053009098024	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	3d6670a58c8fb526
12744	20808	7703243559979823	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3b62dfcb7db43e189b2b4b27a5fc9b95/renderer/components/safeareacontext/States.cpp.o	55a200a3eaa74d05
12923	22603	7703243577719123	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08bf8a5edf40165a2159d743d81643e0/components/safeareacontext/EventEmitters.cpp.o	c3f4490158c9b7ba
12	2451	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	2fadc0b5036c892e
31126	38444	7703243736394376	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	132f3956900de896
12333	27335	7703243624670144	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b2d45095562c584f4d523d595f55b52c/safeareacontext/ComponentDescriptors.cpp.o	f3a22ff6e97b7faf
9392	21538	7703243566763465	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	977658e740e7e943
33201	41345	7703243765513827	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9cf361c9dcbf08115b4e02e5452f90b9/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	dbd72acfeb3ca8e0
10429	23280	7703243584245156	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1ab3867ab8c19783cafcbaefd2e776c5/safeareacontext/RNCSafeAreaViewState.cpp.o	6214269a5bbd623a
68	2885	7704052998453451	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	2a1bd6e3a49ac7bc
13158	24236	7703243594066161	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a1ad7a5abd0bec7351bf381686b7cd20/codegen/jni/safeareacontext-generated.cpp.o	887c82e070108219
12471	24751	7703243599332116	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08bf8a5edf40165a2159d743d81643e0/components/safeareacontext/ShadowNodes.cpp.o	e0249be25afa83c6
24237	33199	7703243683869317	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a7dffbc890b656519e704fbf1db4fec4/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	158a4af727ce5c37
10255	25559	7703243607048598	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1ab3867ab8c19783cafcbaefd2e776c5/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	b4a562f151baa95a
12831	25782	7703243609552938	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3b62dfcb7db43e189b2b4b27a5fc9b95/renderer/components/safeareacontext/Props.cpp.o	f6b63383225966b3
17990	27872	7703243630358408	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0523f5545907f57969e6e42502e3b3ce/safeareacontextJSI-generated.cpp.o	e5a89f96a7d287b9
27873	29148	7703243642705494	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/intermediates/cxx/Debug/5a4h3d62/obj/x86/libreact_codegen_safeareacontext.so	d9cc8ffefeb2337
28311	41919	7703243771028936	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/10cb91d2992f5c02336040eb029a7fe0/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	139811582be270cf
25784	37819	7703243729939784	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	4ddaeb8518e92adf
20809	32726	7703243676119646	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/253b30496f2a8e2da1815fbab0122713/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	fce8abaca0aadf54
16689	29208	7703243643891637	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/afef62fbe82da558c442c4a47d0ab468/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	c5914acec0dcd99b
21539	31125	7703243663149377	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/afef62fbe82da558c442c4a47d0ab468/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	e30b7b7ecdd174b8
23281	32742	7703243678275189	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/afef62fbe82da558c442c4a47d0ab468/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	c4ddd2e5c69bf500
20994	32763	7703243679346315	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c4239ea4df0da3e033d10701a99ff33e/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	bdc5caac12d32179
25636	36702	7703243718902418	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/10cb91d2992f5c02336040eb029a7fe0/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	c33847e5142646fb
22	8224	7704146077971761	CMakeFiles/appmodules.dir/C_/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	ae54fd9941fc2ea2
29209	38469	7703243736588541	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	70dff401c06dc696
24753	38554	7703243737219140	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e4b44120bdf375e36390275b533508e2/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	5830c3271dd9c67c
29149	38801	7703243739988954	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	277471289bd5dc15
32744	39729	7703243749470539	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e4b44120bdf375e36390275b533508e2/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	853feed48c479a93
33961	40960	7703243761741145	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	ca079e63029b2a58
32727	41385	7703243765978077	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	604a5e459e3c2f86
36703	43628	7703243787853053	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	cdec33eb23f8362a
43897	44160	7703243793474037	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/intermediates/cxx/Debug/5a4h3d62/obj/x86/libreact_codegen_rnscreens.so	fec3d3a4c97af842
41	4023	7704053009897439	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	6631d8cfe6022ebc
51	3198	7704053001534621	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	8a7b97df9c984036
82	3719	7704053006802601	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	8fb506bb630d0637
2	73	0	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/.cxx/Debug/5a4h3d62/x86/CMakeFiles/cmake.verify_globs	247cb93bd461a9e3
28	2793	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	2fadc0b5036c892e
2794	3116	7704152630427969	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/intermediates/cxx/Debug/5a4h3d62/obj/x86/libappmodules.so	1dd82b3e242abc46
