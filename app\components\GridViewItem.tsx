import React from "react";
import { View, Text, Pressable, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface GridViewItemProps {
  item: any;
  theme: any;
  onPress: () => void;
  onDelete?: () => void;
  onBookmark?: () => void;
  width?: number;
}

const GridViewItem: React.FC<GridViewItemProps> = ({
  item,
  theme,
  onPress,
  onBookmark,
  width,
}) => {
  const isFolder = item.type === "folder";

  return (
    <Pressable
      style={[styles.gridItemWrapper, { width: width || "100%" }]}
      onPress={onPress}
    >
      {/* Content box */}
      <View
        style={[
          styles.contentBox,
          {
            backgroundColor: theme.card,
            borderColor: theme.border,
          },
        ]}
      >
        {isFolder ? (
          // Folder content
          <View style={styles.folderContent}>
            <View style={styles.folderIconContainer}>
              <Ionicons name="folder" size={40} color={theme.accent} />
            </View>
            <View style={styles.folderInfoContainer}>
              <Text
                style={[styles.fileName, { color: theme.text }]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {item.name}
              </Text>
              <Text style={[styles.itemDate, { color: theme.secondaryText }]}>
                {new Date(
                  item.updatedAt || item.createdAt
                ).toLocaleDateString()}
              </Text>
            </View>
            {item.isBookmarked && (
              <View style={styles.bookmarkIndicator}>
                <Ionicons name="bookmark" size={16} color={theme.accent} />
              </View>
            )}
          </View>
        ) : (
          // Note content
          <View style={styles.noteContent}>
            {item.isBookmarked && (
              <View style={styles.bookmarkIndicator}>
                <Ionicons name="bookmark" size={16} color={theme.accent} />
              </View>
            )}
            <Text
              style={[styles.noteTitle, { color: theme.text }]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {item.title}
            </Text>
            <View style={styles.noteContentContainer}>
              {item.content && (
                <Text
                  style={[
                    styles.noteContentText,
                    { color: theme.secondaryText },
                  ]}
                  numberOfLines={5}
                  ellipsizeMode="tail"
                >
                  {item.content}
                </Text>
              )}
            </View>
            <Text style={[styles.itemDate, { color: theme.secondaryText }]}>
              {new Date(item.updatedAt || item.createdAt).toLocaleDateString()}
            </Text>
          </View>
        )}
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  gridItemWrapper: {
    marginBottom: 16,
    marginHorizontal: 8,
  },
  contentBox: {
    borderRadius: 12,
    overflow: "hidden",
    width: "100%",
    aspectRatio: 0.85, // Maintain consistent aspect ratio
    minHeight: 150, // Ensure minimum height for consistent appearance
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 0,
  },
  folderContent: {
    padding: 16,
    height: "100%",
    position: "relative",
  },
  folderIconContainer: {
    marginBottom: 12,
  },
  folderInfoContainer: {
    position: "absolute",
    bottom: 16,
    left: 16,
    right: 16,
  },
  noteContent: {
    padding: 16,
    height: "100%",
    position: "relative",
    display: "flex",
    flexDirection: "column",
  },
  noteTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
  },
  noteContentContainer: {
    flex: 1,
  },
  noteContentText: {
    fontSize: 14,
    lineHeight: 20,
  },
  fileName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  itemDate: {
    fontSize: 12,
  },
  bookmarkIndicator: {
    position: "absolute",
    top: 12,
    right: 12,
  },
});

export default GridViewItem;
