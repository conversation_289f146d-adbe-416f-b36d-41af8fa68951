import { useState, useEffect } from 'react';
import { Dimensions } from 'react-native';

/**
 * Returns the current orientation of the device
 * @returns {Object} Object containing orientation information
 */
export default function useOrientation() {
  const [screenInfo, setScreenInfo] = useState(getScreenInfo());

  function getScreenInfo() {
    const { width, height } = Dimensions.get('window');
    return {
      width,
      height,
      isPortrait: height > width,
      isLandscape: width > height,
    };
  }

  useEffect(() => {
    const onChange = () => {
      setScreenInfo(getScreenInfo());
    };

    const subscription = Dimensions.addEventListener('change', onChange);

    return () => {
      subscription.remove();
    };
  }, []);

  return screenInfo;
}
