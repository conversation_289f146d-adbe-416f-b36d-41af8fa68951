# ninja log v5
54	729	7704152534113884	build.ninja	50d867ccd7809c9
13	5459	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	127705055bb2660c
2	41	0	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/.cxx/Debug/5a4h3d62/armeabi-v7a/CMakeFiles/cmake.verify_globs	3a92c71b0fa48a97
53	9500	7703242918585837	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	97a1f0267581a793
145	9477	7703242917948911	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o	eb8d4f61e9bbd5f6
74	8336	7703242908080440	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	5c8e42526d0dad09
43	12252	7703242947074413	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	737079f0c7971d74
12237	21095	7703243036053063	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b2d45095562c584f4d523d595f55b52c/safeareacontext/EventEmitters.cpp.o	1eb3b0d5fa484278
33	10692	7703242931630168	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	66e12104a259007b
65	12236	7703242946562471	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	1476c421a92290ea
95	11609	7703242940854110	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	4758bb298710710e
61	4633	7704052883540798	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o	8be40dd56a2a761e
31434	39678	7703243221967301	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	d6b8b5a5d6a06b90
120	11721	7703242941588048	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o	9de7939e4f090f52
24662	42581	7703243250386532	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a782959f22597374393817dbe3aca3b5/components/rnscreens/ComponentDescriptors.cpp.o	6bb9eded1eefabdf
28042	34576	7703243170388404	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	4e93a7944ae21ac4
180	12345	7703242947863440	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	7594b2f902da4fbc
84	12387	7703242948129405	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	46c02ddd673a78d2
21	4459	7704145898266602	CMakeFiles/appmodules.dir/OnLoad.cpp.o	2589eb68426636f7
133	15844	7703242982952550	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o	5f8af07b97ab4689
26763	27633	7703243099700918	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/intermediates/cxx/Debug/5a4h3d62/obj/armeabi-v7a/libreact_codegen_safeareacontext.so	251a841588297ff6
8356	16461	7703242989635149	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o	fe9f2a83773f69cf
11610	18971	7703243014589108	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08bf8a5edf40165a2159d743d81643e0/components/safeareacontext/States.cpp.o	91b1a6e46595d947
14173	25967	7703243084122067	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c4239ea4df0da3e033d10701a99ff33e/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	c837aef2f1a706e8
17	3684	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	c67948c60c0446fa
32534	39833	7703243223398052	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a782959f22597374393817dbe3aca3b5/components/rnscreens/rnscreensJSI-generated.cpp.o	9650b21db279d5bb
9501	21334	7703243038041387	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1c20be6287af9a6f85cf2b6cefc367bb/RNCSafeAreaViewState.cpp.o	594a14266cc22ac8
12388	22162	7703243046140324	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0523f5545907f57969e6e42502e3b3ce/safeareacontextJSI-generated.cpp.o	6ef93cc17b156fd2
21097	32532	7703243150251750	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c4239ea4df0da3e033d10701a99ff33e/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	5426a0624021fbb2
11723	23335	7703243058085346	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b2d45095562c584f4d523d595f55b52c/safeareacontext/ShadowNodes.cpp.o	2137938c8b4352dc
12254	24661	7703243070399725	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08bf8a5edf40165a2159d743d81643e0/components/safeareacontext/Props.cpp.o	6a9885924d4fd0c4
10694	25072	7703243075397502	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1c20be6287af9a6f85cf2b6cefc367bb/RNCSafeAreaViewShadowNode.cpp.o	5818a6f25f5aea33
15845	25569	7703243080648543	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c471ba19136d07ea8dc60c008cd6cf25/jni/safeareacontext-generated.cpp.o	6401953fb3da6934
17218	26743	7703243091575325	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c4239ea4df0da3e033d10701a99ff33e/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	462b57c516433f54
42582	42952	7703243254220239	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/intermediates/cxx/Debug/5a4h3d62/obj/armeabi-v7a/libreact_codegen_rnscreens.so	f90f1944c7214401
12347	26762	7703243091967481	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0523f5545907f57969e6e42502e3b3ce/ComponentDescriptors.cpp.o	98fc5fe49765a636
32763	39188	7703243217100703	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	29b02f706969549f
16462	28022	7703243104628017	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/afef62fbe82da558c442c4a47d0ab468/components/rnscreens/RNSModalScreenShadowNode.cpp.o	1717da6c4c2d07d5
27	11477	7704145967728239	CMakeFiles/appmodules.dir/C_/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	cd838e2ee0462954
20167	29547	7703243119631496	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c4239ea4df0da3e033d10701a99ff33e/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	59dd845b69d4c883
68	3171	7704052868830020	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o	945cf5751f4533c
18972	30859	7703243133253044	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/afef62fbe82da558c442c4a47d0ab468/components/rnscreens/RNSScreenShadowNode.cpp.o	8b3361a7648d685c
33622	41240	7703243237607635	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	605d3d9aab3a9c43
22163	31432	7703243139152493	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4bbd86ab5778fe002729ed3b09dcbe4a/renderer/components/rnscreens/RNSScreenState.cpp.o	3178bd3f3ba7413d
21335	32762	7703243152367177	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c4239ea4df0da3e033d10701a99ff33e/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	199861ab3305ca09
26744	33620	7703243160859615	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/10cb91d2992f5c02336040eb029a7fe0/jni/react/renderer/components/rnscreens/States.cpp.o	b7ab77d0862d2662
11478	11828	7704145971768561	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/intermediates/cxx/Debug/5a4h3d62/obj/armeabi-v7a/libappmodules.so	7ed7caa5e977a9cf
25074	35522	7703243180169288	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/91a4f2b3f2624060758af3aa2879bbf5/react/renderer/components/rnscreens/ShadowNodes.cpp.o	876dbd9c2c62fd3d
23336	35529	7703243180143998	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	176edb030bac0685
27634	36286	7703243187465991	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	5c42a2fa6e3f4513
29548	37416	7703243199242309	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	5b8a95bf13876903
25570	38784	7703243212700193	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/10cb91d2992f5c02336040eb029a7fe0/jni/react/renderer/components/rnscreens/Props.cpp.o	de939dd7eb9a2d7f
30860	38915	7703243214375560	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	6456f6d89381c34c
26017	39800	7703243222786446	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9cf361c9dcbf08115b4e02e5452f90b9/renderer/components/rnscreens/EventEmitters.cpp.o	9f3377cb7f29a14d
33	4513	7704052882307649	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o	9d8578f60540706b
52	3728	7704052874022127	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o	e56b1573eb9e39c2
46	4231	7704052879177426	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o	3001246767235d1f
40	4397	7704052880967239	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o	b24c5b7c6dc07ed2
74	4588	7704052883038771	rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o	2a5ed16565c98227
2	47	0	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/.cxx/Debug/5a4h3d62/armeabi-v7a/CMakeFiles/cmake.verify_globs	3a92c71b0fa48a97
19	2611	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	c67948c60c0446fa
14	3943	0	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	127705055bb2660c
3943	4256	7704152582427362	C:/MyDrive/Projects/Zingtill/mobile/Zingtill-Mobile/android/app/build/intermediates/cxx/Debug/5a4h3d62/obj/armeabi-v7a/libappmodules.so	7ed7caa5e977a9cf
