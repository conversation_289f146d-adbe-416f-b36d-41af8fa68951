import React from "react";
import { View, Text, StyleSheet, StatusBar, Platform } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";

interface NavbarProps {
  showBackButton?: boolean;
  onBackPress?: () => void;
  rightComponent?: React.ReactNode;
}

export default function Navbar({
  showBackButton = false,
  onBackPress,
  rightComponent,
}: NavbarProps) {
  const { theme, isDarkMode } = useTheme();

  // Create dynamic styles based on the current theme
  const dynamicStyles = {
    container: {
      backgroundColor: theme.navbarBackground,
      borderBottomColor: theme.border,
    },
    title: {
      color: theme.text,
    },
  };

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      <StatusBar barStyle={isDarkMode ? "light-content" : "dark-content"} />
      <View style={styles.content}>
        {showBackButton ? (
          <View style={styles.leftContainer}>
            <Ionicons
              name="chevron-back"
              size={24}
              color={theme.primary}
              onPress={onBackPress}
            />
          </View>
        ) : (
          <View style={styles.leftContainer} />
        )}

        <View style={styles.titleContainer}>
          <Text style={[styles.title, dynamicStyles.title]}>ZetPad</Text>
        </View>

        <View style={styles.rightContainer}>{rightComponent}</View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5EA",
    paddingTop: Platform.OS === "ios" ? 50 : StatusBar.currentHeight || 0,
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    height: 50,
    paddingHorizontal: 16,
  },
  leftContainer: {
    width: 40,
    alignItems: "flex-start",
  },
  titleContainer: {
    flex: 1,
    alignItems: "center",
  },
  title: {
    fontSize: 20,
    fontWeight: "600",
    color: "#000000",
  },
  rightContainer: {
    width: 40,
    alignItems: "flex-end",
  },
});
