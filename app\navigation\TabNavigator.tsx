import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Modal,
  Animated,
} from "react-native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";

import HomeStack from "./HomeStack";
import SearchStack from "./SearchStack";
import BookmarksStack from "./BookmarksStack";
import SettingsScreen from "../screens/SettingsScreen";
import AnimatedTabBar from "../components/AnimatedTabBar";
import { useFolders } from "../context/FoldersContext";
import { useCurrentFolder } from "../context/CurrentFolderContext";
import { useTheme } from "../context/ThemeContext";
import { useTabBarVisibility } from "../context/TabBarVisibilityContext";
import { RootStackParamList } from "./index";

// Create the tab navigator
const Tab = createBottomTabNavigator();

// Empty component for the create tab
const EmptyComponent = () => <View />;

export default function TabNavigator() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { createFolder } = useFolders();
  const { currentFolderId } = useCurrentFolder();
  const { theme } = useTheme();
  const { isVisible } = useTabBarVisibility();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();

  const handleCreateNote = (parentId: string | null = null) => {
    // Use the current folder ID if no specific parent ID is provided
    const folderId = parentId || currentFolderId;
    navigation.navigate("CreateNote", { parentId: folderId });
  };

  const handleCreateFolder = () => {
    // Navigate to the folder creation screen with the current folder ID as parent
    navigation.navigate("CreateFolder", { parentId: currentFolderId });
  };

  return (
    <>
      <Tab.Navigator
        tabBar={(props) => <AnimatedTabBar {...props} />}
        screenOptions={{
          headerShown: false,
          // Disable tab transition animations for immediate switching
          tabBarHideOnKeyboard: true,
          // lazy: false, // Pre-load all tabs for instant switching
          tabBarActiveTintColor: theme.primary,
          tabBarInactiveTintColor: theme.secondaryText,
          tabBarShowLabel: false, // Hide all tab labels
        }}
      >
        <Tab.Screen
          name="Home"
          component={HomeStack}
          options={{
            tabBarIcon: ({ focused, color }) => (
              <Ionicons
                name={focused ? "home" : "home-outline"}
                size={24}
                color={color}
              />
            ),
          }}
        />
        <Tab.Screen
          name="Search"
          component={SearchStack}
          options={{
            tabBarIcon: ({ focused, color }) => (
              <Ionicons
                name={focused ? "search" : "search-outline"}
                size={24}
                color={color}
              />
            ),
          }}
        />
        <Tab.Screen
          name="Create"
          component={EmptyComponent}
          listeners={{
            tabPress: (e) => {
              // Prevent default behavior
              e.preventDefault();
              // Show modal
              setIsModalOpen(true);
            },
          }}
          options={{
            tabBarIcon: () => (
              <View style={styles.addButtonContainer}>
                <View
                  style={[styles.addButton, { backgroundColor: theme.primary }]}
                >
                  <Ionicons name="add" size={24} color="#FFFFFF" />
                </View>
              </View>
            ),
            tabBarLabel: () => null,
          }}
        />
        <Tab.Screen
          name="Bookmarks"
          component={BookmarksStack}
          options={{
            tabBarIcon: ({ focused, color }) => (
              <Ionicons
                name={focused ? "bookmark" : "bookmark-outline"}
                size={24}
                color={color}
              />
            ),
          }}
        />
        <Tab.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            tabBarIcon: ({ focused, color }) => (
              <Ionicons
                name={focused ? "settings" : "settings-outline"}
                size={24}
                color={color}
              />
            ),
          }}
        />
      </Tab.Navigator>

      <Modal
        visible={isModalOpen}
        transparent
        animationType="fade"
        onRequestClose={() => setIsModalOpen(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.card }]}>
            <Text style={[styles.modalTitle, { color: theme.text }]}>
              Create New
            </Text>

            <Pressable
              style={[
                styles.modalOption,
                { backgroundColor: theme.background },
              ]}
              onPress={() => {
                setIsModalOpen(false);
                handleCreateNote();
              }}
            >
              <Ionicons
                name="document-text-outline"
                size={24}
                color={theme.primary}
              />
              <Text style={[styles.modalOptionText, { color: theme.text }]}>
                New Note
              </Text>
            </Pressable>

            <Pressable
              style={[
                styles.modalOption,
                { backgroundColor: theme.background },
              ]}
              onPress={() => {
                setIsModalOpen(false);
                handleCreateFolder();
              }}
            >
              <Ionicons name="folder-outline" size={24} color={theme.accent} />
              <Text style={[styles.modalOptionText, { color: theme.text }]}>
                New Folder
              </Text>
            </Pressable>

            <Pressable
              style={[
                styles.cancelButton,
                { backgroundColor: theme.background },
              ]}
              onPress={() => setIsModalOpen(false)}
            >
              <Text style={[styles.cancelButtonText, { color: theme.danger }]}>
                Cancel
              </Text>
            </Pressable>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  addButtonContainer: {
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
    height: 60,
  },
  addButton: {
    width: 40, // Reduced from 50 to 40
    height: 40, // Reduced from 50 to 40
    borderRadius: 20, // Half of width/height
    backgroundColor: "#007AFF",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 0,
    marginTop: 0, // Reduced from 10 to 5
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#FFFFFF",
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
    textAlign: "center",
  },
  modalOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: "#F2F2F7",
  },
  modalOptionText: {
    fontSize: 16,
    marginLeft: 12,
    color: "#000000",
  },
  cancelButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
    backgroundColor: "#F2F2F7",
    marginTop: 8,
  },
  cancelButtonText: {
    color: "#FF3B30",
    fontSize: 16,
    fontWeight: "600",
  },
});
