import React, { useEffect, useRef } from "react";
import { View, Animated, StyleSheet, Pressable, Platform } from "react-native";
import { BottomTabBarProps } from "@react-navigation/bottom-tabs";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTabBarVisibility } from "../context/TabBarVisibilityContext";
import { useTheme } from "../context/ThemeContext";
import useOrientation from "../hooks/useOrientation";

export default function AnimatedTabBar({
  state,
  descriptors,
  navigation,
}: BottomTabBarProps) {
  const { isVisible } = useTabBarVisibility();
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const orientation = useOrientation();

  // Animation value for the tab bar
  const translateY = useRef(new Animated.Value(0)).current;

  // Update animation when visibility changes
  useEffect(() => {
    Animated.spring(translateY, {
      toValue: isVisible ? 0 : 100,
      useNativeDriver: true,
      friction: 8,
      tension: 50,
    }).start();
  }, [isVisible, translateY]);

  // Force re-render when orientation changes to update insets
  useEffect(() => {
    // When orientation changes, we need to recalculate the layout
    // This is especially important for Android navigation bar handling
    const timeout = setTimeout(() => {
      // Force a re-render after a short delay to ensure all layout calculations are complete
      // This helps with the Android navigation bar issue
      translateY.setValue(isVisible ? 0 : 100);
    }, 150);

    return () => clearTimeout(timeout);
  }, [orientation.isLandscape, isVisible]);

  // Calculate bottom padding based on orientation and platform
  const bottomPadding =
    Platform.OS === "android"
      ? orientation.isLandscape
        ? Math.max(insets.bottom, 8) // Add padding in landscape on Android
        : Math.max(insets.bottom, 8) // At least 8px in portrait mode
      : insets.bottom; // Use safe area insets on iOS

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: theme.tabBar,
          borderTopColor: theme.border,
          transform: [{ translateY }],
          paddingBottom: bottomPadding,
          height: 50 + bottomPadding, // Adjust height based on bottom padding
        },
      ]}
    >
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: "tabPress",
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: "tabLongPress",
            target: route.key,
          });
        };

        // Use the tabBarIcon from the options
        const icon = options.tabBarIcon
          ? options.tabBarIcon({
              focused: isFocused,
              color: isFocused ? theme.primary : theme.secondaryText,
              size: 24,
            })
          : null;

        return (
          <View key={route.key} style={styles.tab}>
            <Pressable
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={onPress}
              onLongPress={onLongPress}
              style={styles.tabButton}
            >
              {icon}
            </Pressable>
          </View>
        );
      })}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    height: 50, // Base height without insets
    borderTopWidth: 1,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    zIndex: 999, // Ensure it's above other elements
  },
  tab: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  tabButton: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
});
