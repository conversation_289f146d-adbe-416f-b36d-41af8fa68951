import { View, StyleSheet, Text } from "react-native";
import BookmarksContent from "../components/BookmarksContent";
import { useTheme } from "../context/ThemeContext";
import Navbar from "../components/Navbar";
import ViewModeToggle from "../components/ViewModeToggle";

export default function BookmarksScreen() {
  const { theme } = useTheme();

  // Create dynamic styles based on the current theme
  const dynamicStyles = {
    container: {
      backgroundColor: theme.background,
    },
    title: {
      color: theme.text,
    },
  };

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      <Navbar rightComponent={<ViewModeToggle />} />
      <Text style={[styles.title, dynamicStyles.title]}>Bookmarks</Text>
      <BookmarksContent />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 24,
    paddingHorizontal: 20,
  },
});
