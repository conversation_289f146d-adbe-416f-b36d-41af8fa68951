import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  Switch,
  Alert,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";
import Navbar from "../components/Navbar";
import ThemedModal from "../components/ThemedModal";

export default function SettingsScreen() {
  const { isDarkMode, toggleTheme, theme } = useTheme();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  // State for modals
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [clearModalVisible, setClearModalVisible] = useState(false);

  // Export data handlers
  const handleExportData = () => {
    setExportModalVisible(true);
  };

  const handleExportConfirm = () => {
    console.log("Export data");
    setExportModalVisible(false);
  };

  // Import data handlers
  const handleImportData = () => {
    setImportModalVisible(true);
  };

  const handleImportConfirm = () => {
    console.log("Import data");
    setImportModalVisible(false);
  };

  // Clear data handlers
  const handleClearData = () => {
    setClearModalVisible(true);
  };

  const handleClearConfirm = () => {
    console.log("Clear data");
    setClearModalVisible(false);
  };

  const renderSettingItem = (
    icon: string,
    title: string,
    rightElement?: React.ReactNode,
    onPress?: () => void
  ) => (
    <Pressable
      style={[styles.settingItem, dynamicStyles.settingItem]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingIconContainer}>
        <Ionicons name={icon as any} size={22} color={theme.primary} />
      </View>
      <Text style={[styles.settingTitle, dynamicStyles.settingTitle]}>
        {title}
      </Text>
      <View style={styles.settingRight}>{rightElement}</View>
    </Pressable>
  );

  // Create dynamic styles based on the current theme
  const dynamicStyles = {
    container: {
      backgroundColor: theme.background,
    },
    header: {
      backgroundColor: theme.background,
    },
    title: {
      color: theme.text,
    },
    section: {
      backgroundColor: theme.card,
    },
    sectionTitle: {
      color: theme.secondaryText,
    },
    settingTitle: {
      color: theme.text,
    },
    settingItem: {
      borderBottomColor: theme.border,
    },
    versionText: {
      color: theme.secondaryText,
    },
  };

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      <Navbar />
      <View style={[styles.header, dynamicStyles.header]}>
        <Text style={[styles.title, dynamicStyles.title]}>Settings</Text>
      </View>

      {/* Export Data Modal */}
      <ThemedModal
        visible={exportModalVisible}
        title="Export Data"
        message="This will export all your notes and folders as a JSON file."
        onCancel={() => setExportModalVisible(false)}
        onConfirm={handleExportConfirm}
        confirmText="Export"
      />

      {/* Import Data Modal */}
      <ThemedModal
        visible={importModalVisible}
        title="Import Data"
        message="This will import notes and folders from a JSON file. This will overwrite your current data."
        onCancel={() => setImportModalVisible(false)}
        onConfirm={handleImportConfirm}
        confirmText="Import"
      />

      {/* Clear Data Modal */}
      <ThemedModal
        visible={clearModalVisible}
        title="Clear All Data"
        message="This will permanently delete all your notes and folders. This action cannot be undone."
        onCancel={() => setClearModalVisible(false)}
        onConfirm={handleClearConfirm}
        confirmText="Clear"
        isDestructive={true}
      />

      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={[styles.section, dynamicStyles.section]}>
          <Text style={[styles.sectionTitle, dynamicStyles.sectionTitle]}>
            Appearance
          </Text>
          {renderSettingItem(
            "moon-outline",
            "Dark Mode",
            <Switch
              value={isDarkMode}
              onValueChange={toggleTheme}
              trackColor={{ false: "#D1D1D6", true: theme.primary }}
              thumbColor="#FFFFFF"
            />
          )}
        </View>

        <View style={[styles.section, dynamicStyles.section]}>
          <Text style={[styles.sectionTitle, dynamicStyles.sectionTitle]}>
            Notifications
          </Text>
          {renderSettingItem(
            "notifications-outline",
            "Enable Notifications",
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: "#D1D1D6", true: theme.primary }}
              thumbColor="#FFFFFF"
            />
          )}
        </View>

        <View style={[styles.section, dynamicStyles.section]}>
          <Text style={[styles.sectionTitle, dynamicStyles.sectionTitle]}>
            Data
          </Text>
          {renderSettingItem(
            "cloud-upload-outline",
            "Export Data",
            <Ionicons
              name="chevron-forward"
              size={20}
              color={theme.secondaryText}
            />,
            handleExportData
          )}
          {renderSettingItem(
            "cloud-download-outline",
            "Import Data",
            <Ionicons
              name="chevron-forward"
              size={20}
              color={theme.secondaryText}
            />,
            handleImportData
          )}
          {renderSettingItem(
            "trash-outline",
            "Clear All Data",
            <Ionicons
              name="chevron-forward"
              size={20}
              color={theme.secondaryText}
            />,
            handleClearData
          )}
        </View>

        <View style={[styles.section, dynamicStyles.section]}>
          <Text style={[styles.sectionTitle, dynamicStyles.sectionTitle]}>
            About
          </Text>
          {renderSettingItem(
            "information-circle-outline",
            "Version",
            <Text style={[styles.versionText, dynamicStyles.versionText]}>
              1.0.0
            </Text>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f2f2f7",
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: "#f2f2f7",
  },
  title: {
    fontSize: 34,
    fontWeight: "bold",
    color: "#000000",
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 80, // Add extra padding at the bottom for the tab bar
  },
  section: {
    marginTop: 20,
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    marginHorizontal: 16,
    overflow: "hidden",
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: "600",
    color: "#8E8E93",
    marginBottom: 8,
    marginHorizontal: 16,
    marginTop: 5,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5EA",
  },
  settingIconContainer: {
    width: 30,
    alignItems: "center",
    marginRight: 10,
  },
  settingTitle: {
    flex: 1,
    fontSize: 17,
    color: "#000000",
  },
  settingRight: {
    marginLeft: 10,
  },
  versionText: {
    fontSize: 17,
    color: "#8E8E93",
  },
});
